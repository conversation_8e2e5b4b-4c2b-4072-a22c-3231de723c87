import { request } from '~/utils/http'
import { Game, GameAccount } from './types'

// 导出所有类型
export * from './types'

// 游戏相关API
export const gameApi = {
  // 获取游戏列表
  getGameList: () => request<Game[]>({
    url: '/api/games',
    method: 'GET',
  }),
  
  // 获取游戏详情
  getGameDetail: (gameId: string) => request<Game>({
    url: `/api/games/${gameId}`,
    method: 'GET',
  }),
  
  // 获取用户在特定游戏中的账户
  getGameAccounts: (gameId: string) => request<GameAccount[]>({
    url: `/api/games/${gameId}/accounts`,
    method: 'GET',
  }),
  
  // 绑定游戏账号
  bindGameAccount: (gameId: string, data: { uid: string, bindKey: string }) => 
    request<{ success: boolean }>({
      url: `/api/games/${gameId}/bind`,
      method: 'POST',
      data, 
    }),
  
  // 获取游戏绑定密钥
  getBindKey: (gameId: string) => request<{ key: string }>({
    url: `/api/games/${gameId}/bindKey`,
    method: 'GET',
  }),
}
