/**
 * OAuth重定向绑定处理器
 * 处理通过OAuth重定向到游戏官方登录页面进行的账号绑定
 */

import { 
  BindingType, 
  BindingParams, 
  BindingResult, 
  GameBindingConfig 
} from '@/types/game-binding'
import { BaseBindingHandler } from './BaseBindingHandler'

export class OAuthBindingHandler extends BaseBindingHandler {
  type = BindingType.OAUTH

  /**
   * 执行OAuth绑定
   */
  async bind(params: BindingParams, config: GameBindingConfig): Promise<BindingResult> {
    const { gameId, formData, additionalData } = params

    try {
      // OAuth绑定通常不需要表单验证，而是通过重定向处理
      if (additionalData?.authCode) {
        // 如果有授权码，说明是从回调页面返回的
        return await this.handleOAuthCallback(params, config)
      } else {
        // 否则启动OAuth流程
        return await this.initiateOAuthFlow(params, config)
      }

    } catch (error: any) {
      console.error('OAuth绑定失败:', error)
      return this.formatBindingResult(false, null, error.message || 'OAuth绑定失败')
    }
  }

  /**
   * 启动OAuth授权流程
   */
  private async initiateOAuthFlow(
    params: BindingParams, 
    config: GameBindingConfig
  ): Promise<BindingResult> {
    if (!config.oauthConfig) {
      return this.formatBindingResult(false, null, 'OAuth配置缺失')
    }

    try {
      // 构建授权URL
      const authUrl = this.buildAuthUrl(config)
      
      // 重定向到授权页面
      window.location.href = authUrl
      
      // 返回pending状态，表示正在进行OAuth流程
      return this.formatBindingResult(true, {
        gameId: params.gameId,
        status: 'redirecting',
        authUrl
      }, '正在跳转到授权页面...')

    } catch (error: any) {
      return this.formatBindingResult(false, null, error.message || '启动OAuth流程失败')
    }
  }

  /**
   * 处理OAuth回调
   */
  private async handleOAuthCallback(
    params: BindingParams, 
    config: GameBindingConfig
  ): Promise<BindingResult> {
    const { gameId, additionalData } = params

    try {
      // 使用授权码换取访问令牌
      const tokenResponse = await this.exchangeCodeForToken(
        additionalData!.authCode, 
        config
      )

      if (!tokenResponse.access_token) {
        return this.formatBindingResult(false, null, '获取访问令牌失败')
      }

      // 使用访问令牌获取用户信息
      const userInfo = await this.getUserInfo(tokenResponse.access_token, config)

      if (!userInfo) {
        return this.formatBindingResult(false, null, '获取用户信息失败')
      }

      // 调用绑定API
      const bindData = {
        gameId,
        accessToken: tokenResponse.access_token,
        userInfo
      }

      const response = await this.makeRequest<any>(
        'POST',
        config.apiConfig.bindEndpoint,
        bindData
      )

      if (response.success !== false) {
        return this.formatBindingResult(true, {
          gameId,
          characterName: userInfo.nickname || userInfo.name,
          accountUid: userInfo.uid || userInfo.id,
          bindingId: response.bindingId
        }, 'OAuth绑定成功')
      } else {
        return this.formatBindingResult(false, null, response.message || 'OAuth绑定失败')
      }

    } catch (error: any) {
      console.error('OAuth回调处理失败:', error)
      return this.formatBindingResult(false, null, error.message || 'OAuth回调处理失败')
    }
  }

  /**
   * 构建授权URL
   */
  private buildAuthUrl(config: GameBindingConfig): string {
    const oauthConfig = config.oauthConfig!
    const params = new URLSearchParams({
      client_id: oauthConfig.clientId,
      redirect_uri: oauthConfig.callbackUrl,
      response_type: 'code',
      scope: oauthConfig.scope || 'read',
      state: this.generateState(config.gameId)
    })

    return `${oauthConfig.authUrl}?${params.toString()}`
  }

  /**
   * 使用授权码换取访问令牌
   */
  private async exchangeCodeForToken(
    authCode: string, 
    config: GameBindingConfig
  ): Promise<any> {
    const oauthConfig = config.oauthConfig!
    
    const tokenData = {
      grant_type: 'authorization_code',
      client_id: oauthConfig.clientId,
      code: authCode,
      redirect_uri: oauthConfig.callbackUrl
    }

    // 这里应该调用游戏的token端点，但由于每个游戏的实现不同，
    // 这里使用通用的处理方式
    return await this.makeRequest<any>(
      'POST',
      `${config.apiConfig.bindEndpoint}/token`,
      tokenData
    )
  }

  /**
   * 获取用户信息
   */
  private async getUserInfo(accessToken: string, config: GameBindingConfig): Promise<any> {
    return await this.makeRequest<any>(
      'GET',
      `${config.apiConfig.bindEndpoint}/userinfo`,
      { access_token: accessToken }
    )
  }

  /**
   * 生成状态参数（用于防止CSRF攻击）
   */
  private generateState(gameId: string): string {
    const timestamp = Date.now().toString()
    const random = Math.random().toString(36).substring(2)
    return `${gameId}_${timestamp}_${random}`
  }

  /**
   * 验证状态参数
   */
  private validateState(state: string, expectedGameId: string): boolean {
    const parts = state.split('_')
    return parts.length === 3 && parts[0] === expectedGameId
  }

  /**
   * 获取自定义组件
   */
  getCustomComponents() {
    return {
      // OAuth授权按钮
      oauthButton: {
        text: '跳转到游戏官方登录',
        action: 'initiateOAuth'
      }
    }
  }
}
