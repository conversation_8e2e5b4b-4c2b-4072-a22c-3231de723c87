<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BindingForm from './components/BindingForm.vue'
import { useGameBinding } from './composables/useGameBinding'

const router = useRouter()
const { loadGameConfig } = useGameBinding()

// 页面初始化
onMounted(async () => {
  const success = await loadGameConfig()
  if (!success) {
    // 如果游戏不支持或配置加载失败，跳转到游戏列表
    router.push('/game/list')
  }
})
</script>

<template>
  <div class="game-binding-page h-full w-full">
    <BindingForm />
  </div>
</template>

<style scoped>
.game-binding-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style>

<route lang="json">
{
  "meta": {
    "layout": "backHeader",
    "title": "游戏账号绑定"
  }
}
</route>
