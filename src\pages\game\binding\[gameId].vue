<script setup lang="ts">
import GameBinding from '@/components/GameBinding/index.vue'
import { useRoute } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()

// 获取游戏ID和名称用于页面标题
const gameId = computed(() => route.params.gameId as string)
const gameName = computed(() => route.query.gameName as string || '游戏')

// 动态设置页面标题
const pageTitle = computed(() => `${gameName.value}账号绑定`)
</script>

<template>
  <div class="game-binding-page h-full w-full">
    <GameBinding />
  </div>
</template>

<style scoped>
.game-binding-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style>

<route lang="json">
{
  "meta": {
    "layout": "backHeader",
    "title": "游戏账号绑定"
  }
}
</route>
