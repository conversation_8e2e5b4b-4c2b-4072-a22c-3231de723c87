// 游戏信息
export interface Game {
  id: string
  name: string
  imageUrl: string
  themeId?: number
  tagUrl?: string // 游戏标签图片
  isRecent?: boolean // 是否为最近玩过的游戏
  description?: string // 游戏描述
}

// 游戏账户信息
export interface GameAccount {
  id: string
  name: string
  avatar: string
  gameId: string | number
  gameName: string
  active?: boolean // 是否为激活账号
}

// 绑定游戏请求参数
export interface BindGameParams {
  uid: string
  bindKey: string
  gameId: string
}

// 绑定游戏响应结果
export interface BindGameResult {
  success: boolean
  message?: string
  accountInfo?: GameAccount
}

// 游戏绑定密钥
export interface BindKeyInfo {
  key: string
  expireTime: number // 过期时间戳
}

/**
 * 获取绑定验证码参数
 */
export interface GetBindCodeParams {
  gameId: number
  phone?: string
}

/**
 * 绑定验证码响应
 */
export interface BindCodeResponse {
  bindCode: string
  expireTime: number
}

/**
 * 获取绑定历史参数
 */
export interface BindHistoryParams {
  gameId?: number
  pageSize?: number
  pageNum?: number
}

/**
 * 绑定历史记录
 */
export interface BindHistoryItem {
  id: string
  gameId: number
  gameName: string
  uid: string
  nickname: string
  bindTime: string
  status: number
}

/**
 * 绑定历史列表响应
 */
export interface BindHistoryResponse {
  total: number
  list: BindHistoryItem[]
}

/**
 * 基础API响应
 */
export interface BaseResponse<T> {
  code: number
  message: string
  data: T
} 