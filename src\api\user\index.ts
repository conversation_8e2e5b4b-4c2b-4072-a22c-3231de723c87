import type { UserInfo, LoginParams } from './types'
import { request } from '~/utils/http'

export const userApi = {
  // 登录
  login: (data: LoginParams) => request<UserInfo>({
    url: '/api/login',
    method: 'POST',
    data,
  }),
  
  // 获取用户信息
  getInfo: () => request<UserInfo>({
    url: '/api/user/info',
    method: 'GET',
  }),
  
  // 登出
  logout: () => request<UserInfo>({
    url: '/api/logout',
    method: 'POST',
  }),
  
  // 更新用户信息
  update: (data: Partial<UserInfo>) => request<UserInfo>({
    url: '/api/user/info',
    method: 'PUT',
    data,
  }),
} 