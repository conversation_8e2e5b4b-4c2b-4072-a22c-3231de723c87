<script setup lang="ts">
import CustomSelect from '@/components/Select/index.vue'
import BindResult from '@/components/BindResult/index.vue'
import { reactive, ref } from 'vue'

// 添加类型定义
interface FormField {
  model: string
  placeholder: string
  label: string
  type?: string
  options?: Array<{ value: string, label: string }>
  required?: boolean
}

interface BindResponse {
  game_id?: string
  gameId?: string
  character_name?: string
  role_name?: string
  account_uid?: string
  accountUid?: string
  uid?: string
}

// 更新 Props 定义
const props = defineProps<{
  logoSrc?: string
  inputFields: FormField[]
  bindFunction: (formData: Record<string, any>) => Promise<BindResponse>
  additionalData: Record<string, any>
  customButtonString: string
  showResultInForm?: boolean
}>()

const data = reactive({
  FormData: {} as Record<string, any>,
  logo: props.logoSrc,
  ...props.additionalData,
})

// 管理输入框焦点状态
const activeInputs = reactive<Record<string, boolean>>({})

// 设置输入框焦点状态
function setInputFocus(model: string, isFocused: boolean) {
  activeInputs[model] = isFocused
}

const bindingResultVisible = ref(false)
const bindBtnLoading = ref(false)
const bindingSuccess = ref(false)
const bindingErrorMessage = ref('')
const router = useRouter()
const emit = defineEmits(['bindSuccess', 'bindError'])

// 更新 Bind button function
async function bindUserBtn() {
  if (bindBtnLoading.value)
    return // 防止重复点击

  bindBtnLoading.value = true
  try {
    // 验证必填字段
    const invalidField = props.inputFields.find((field) => {
      const isRequired = field.required ?? true
      return isRequired && !data.FormData[field.model]
    })

    if (invalidField) {
      bindingErrorMessage.value = `请填写${invalidField.label}`
      return
    }

    // 调用绑定函数
    const userData = await props.bindFunction(data.FormData)
    if (!userData) {
      throw new Error('绑定失败，请稍后重试')
    }

    // 提取ID信息
    const game_id = userData.game_id
      ?? userData.gameId
      ?? userData.character_name
      ?? userData.role_name
      ?? ''

    const account_uid = userData.account_uid
      ?? userData.accountUid
      ?? userData.uid
      ?? ''

    if (!game_id || !account_uid) {
      throw new Error('获取用户信息失败')
    }

    // 更新状态
    data.gameId = game_id
    bindingSuccess.value = true
    
    // 如果设置了在表单内显示结果
    if (props.showResultInForm) {
      bindingResultVisible.value = true
    }
    
    // 触发绑定成功事件
    emit('bindSuccess', userData)
  }
  catch (error: any) {
    console.error('绑定过程出错:', error)
    bindingErrorMessage.value = error.message || '绑定失败，请稍后重试'
    bindingSuccess.value = false
    
    // 如果设置了在表单内显示结果
    if (props.showResultInForm) {
      bindingResultVisible.value = true
    }
    
    // 触发绑定失败事件
    emit('bindError', error)
  }
  finally {
    bindBtnLoading.value = false
  }
}

// 清除输入框内容的函数
function clearInput(model: string) {
  data.FormData[model] = ''
}

// 处理绑定结果确认事件
function handleResultConfirm() {
  bindingResultVisible.value = false
  // 可以添加更多的成功后逻辑，如跳转页面等
}

// 处理绑定结果重试事件
function handleResultRetry() {
  bindingResultVisible.value = false
  // 可以清空某些字段或重置状态
}

defineExpose({
  data,
  bindingSuccess,
  bindingErrorMessage
})
</script>

<template>
  <div class="h-full w-full relative">
    <!-- 绑定表单 -->
    <div v-if="!bindingResultVisible" class="flex flex-col min-h-full w-full overflow-auto">
      <div class="flex-grow flex flex-col items-center w-full overflow-y-auto">
        <div class="h-[180px] flex items-center justify-center" v-if="data.logo">
          <var-image :src="data.logo" width="186" />
        </div>
        <div class="relative w-full px-8">
          <div 
            v-for="field in props.inputFields" 
            :key="field.model" 
            class="mb-3"
          >
            <span class="text-base font-normal text-[#1C1C1C] font-['PingFang_SC'] leading-1.4em block mb-1">{{ field.label }}</span>
            
            <!-- 选择器 -->
            <CustomSelect
              v-if="field.type === 'select'"
              v-model="data.FormData[field.model]"
              :options="field.options"
              :placeholder="field.placeholder"
            />
            
            <!-- 普通输入框 -->
            <div v-else class="relative my-2">
              <div 
                class="w-full relative"
                :class="{ 'border-[#FFD722] border rounded-4': activeInputs[field.model] }"
              >
                <input
                  v-model="data.FormData[field.model]"
                  :placeholder="field.placeholder"
                  class="w-full py-[10px] px-4 text-sm bg-[#EDEDED] rounded-4 h-[40px] border-none outline-none placeholder:text-[#B8B8B8]"
                  @focus="setInputFocus(field.model, true)"
                  @blur="setInputFocus(field.model, false)"
                >
                <var-button
                  v-if="data.FormData[field.model]"
                  class="absolute right-3 top-1/2 transform -translate-y-1/2 p-0 h-auto min-w-0 bg-transparent text-[#B8B8B8]"
                  text
                  size="mini"
                  @click="clearInput(field.model)"
                >
                  ✕
                </var-button>
              </div>
              
              <!-- 输入框特定的自定义按钮插槽 -->
              <slot :name="`custom-button-${field.model}`"></slot>
            </div>
          </div>
          
          <!-- 全局自定义按钮插槽 -->
          <slot name="custom-button"></slot>
          
          <div class="w-full">
            <var-button
              type="primary"
              class="mt-4 h-[48px] w-full rounded-2xl bg-[#FFD722] text-[#333] font-medium text-base border-none"
              :class="{ 'opacity-70': bindBtnLoading }"
              :loading="bindBtnLoading"
              @click="bindUserBtn"
              :elevation="false"
              text
            >
              {{ !bindBtnLoading ? props.customButtonString || '绑定' : '绑定中...' }}
            </var-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 绑定结果展示 -->
    <BindResult 
      v-if="bindingResultVisible"
      :success="bindingSuccess"
      :message="bindingSuccess ? '绑定成功' : undefined"
      :error-message="bindingErrorMessage"
      @confirm="handleResultConfirm"
      @retry="handleResultRetry"
    />
  </div>
  <div class="absolute bottom-0 left-0 right-0 flex items-center justify-center h-[100px]">
    <div class="text-sm text-[#B8B8B8]">
      遇到问题？点此
      <span class="text-[#FFD722]" @click="router.push('/contact')">联系客服</span>
    </div>
  </div>
</template>

