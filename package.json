{"name": "tov-template", "version": "1.19.0", "description": "vite + vue3 + ts 开箱即用现代开发模板 | vite + vue3 + ts out-of-the-box modern development template", "type": "module", "scripts": {"dev": "vite", "test": "vitest", "build": "vite build", "dev:host": "vite --host", "dev:open": "vite --open", "preview": "vite preview", "coverage": "vitest --coverage", "preinstall": "npx only-allow pnpm", "typecheck": "npx vue-tsc --noEmit", "preview:host": "vite preview --host", "preview:open": "vite preview --open", "lint": "eslint --ext .ts,.js,.jsx,.vue .", "release": "plop --plopfile scripts/release.cjs", "auto:remove": "plop --plopfile scripts/remove.cjs", "auto:create": "plop --plopfile scripts/create.cjs", "build:debug": "cross-env NODE_ENV=debug vite build", "safe:init": "plop --plopfile scripts/safe-init.cjs", "deps:fresh": "plop --plopfile scripts/deps-fresh.cjs", "lint:fix": "eslint --fix --ext .ts,.js,.jsx,.vue,.cjs ."}, "engines": {"node": ">=20.12.2"}, "packageManager": "pnpm@8.15.8", "devDependencies": {"@iconify-json/carbon": "^1.2.11", "@iconify-json/famicons": "^1.2.0", "@iconify-json/heroicons-outline": "^1.2.1", "@iconify-json/mdi": "^1.2.3", "@iconify-json/streamline": "^1.2.5", "@iconify-json/tabler": "^1.2.20", "@types/ityped": "^1.0.3", "@types/node": "^24.1.0", "@typescript-eslint/parser": "8.34.0", "@unocss/eslint-config": "66.1.4", "@unocss/reset": "^66.3.3", "@varlet/preset-unocss": "^3.11.1", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vueuse/components": "^13.5.0", "@vueuse/core": "^13.5.0", "@vueuse/integrations": "^13.5.0", "axios": "^1.11.0", "browserslist": "^4.25.1", "c8": "^10.1.3", "changelogen": "^0.6.2", "consola": "^3.4.2", "cross-env": "^7.0.3", "defu": "^6.1.4", "echarts": "^5.6.0", "eslint": "9.28.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-vue": "^10.3.0", "fs-extra": "^11.3.0", "ityped": "^1.0.3", "kolorist": "^1.8.0", "lightningcss": "^1.30.1", "lint-staged": "^16.1.2", "local-pkg": "^1.1.1", "markdown-it-prism": "^3.0.0", "nprogress": "^0.2.0", "perfect-debounce": "^1.0.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "plop": "^4.0.1", "prettier": "^3.6.2", "prism-theme-vars": "^0.2.5", "simple-git": "^3.28.0", "simple-git-hooks": "^2.13.0", "taze": "^19.1.0", "terser": "^5.43.1", "typescript": "^5.8.3", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "unplugin-vue-markdown": "^28.3.1", "unplugin-vue-router": "^0.12.0", "vite": "^6.3.5", "vite-auto-import-resolvers": "^3.2.1", "vite-layers": "^0.5.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-env-types": "^0.1.4", "vite-plugin-legacy-swc": "^1.2.3", "vite-plugin-removelog": "^0.2.2", "vite-plugin-use-modules": "^1.4.8", "vite-plugin-vue-devtools": "^7.7.7", "vite-plugin-vue-meta-layouts": "^0.5.1", "vitest": "^3.2.4", "vue": "^3.5.18", "vue-dark-switch": "^1.0.6", "vue-echarts": "^7.0.3", "vue-request": "2.0.4", "vue-router": "^4.5.1", "vue-toastification": "2.0.0-rc.5"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": "eslint --cache --fix"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "overrides": {"sourcemap-codec": "npm:@jridgewell/sourcemap-codec@latest", "array-includes": "npm:@nolyfill/array-includes@latest", "array.prototype.findlastindex": "npm:@nolyfill/array.prototype.findlastindex@latest", "array.prototype.flat": "npm:@nolyfill/array.prototype.flat@latest", "array.prototype.flatmap": "npm:@nolyfill/array.prototype.flatmap@latest", "arraybuffer.prorotype.slice": "npm:@nolyfill/arraybuffer.prorotype.slice@latest", "function.prototype.name": "npm:@nolyfill/function.prototype.name@latest", "has": "npm:@nolyfill/has@latest", "is-regex": "npm:@nolyfill/is-regex@latest", "object-keys": "npm:@nolyfill/object-keys@latest", "object.assign": "npm:@nolyfill/object.assign@latest", "object.entries": "npm:@nolyfill/object.entries@latest", "object.fromentries": "npm:@nolyfill/object.fromentries@latest", "object.values": "npm:@nolyfill/object.values@latest", "vue-demi": "npm:vue-demi@latest"}, "repository": {"url": "https://github.com/dishait/tov-template"}, "browserslist": [">= 0.25%", "last 2 versions", "not dead", "not ie <= 11", "Android >= 4.0", "iOS >= 8"], "dependencies": {"@microsoft/clarity": "^1.0.0", "@neysf/qiyu-web-sdk": "^1.1.6", "@sentry/browser": "^9.42.0", "@sentry/tracing": "^7.120.3", "@sentry/vue": "^9.42.0", "@varlet/import-resolver": "^3.11.1", "@varlet/ui": "^3.11.1", "mitt": "^3.0.1", "pinyin-pro": "^3.26.0", "qs": "^6.14.0", "vite-plugin-pwa": "^1.0.2"}}