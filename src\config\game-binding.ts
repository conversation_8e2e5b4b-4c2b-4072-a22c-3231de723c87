/**
 * 游戏绑定配置数据
 */

import { 
  GameBindingConfig, 
  BindingType, 
  FieldType 
} from '@/types/game-binding'

// 月圆之夜 (5199) - 支持多标签页绑定
export const game5199Config: GameBindingConfig = {
  gameId: '5199',
  gameName: '月圆之夜',
  logoUrl: '/images/games/5199-logo.png',
  bindingType: BindingType.API,
  formFields: [
    {
      model: 'roleName',
      label: '角色昵称',
      placeholder: '请输入游戏中的角色昵称',
      type: FieldType.TEXT,
      required: true
    },
    {
      model: 'gameUid',
      label: '游戏UID',
      placeholder: '请输入游戏中的UID',
      type: FieldType.TEXT,
      required: true,
      validation: {
        minLength: 8,
        maxLength: 12,
        pattern: '^[a-zA-Z0-9]+$',
        validator: (value: string) => {
          if (!/^[a-zA-Z0-9]+$/.test(value)) {
            return '游戏UID只能包含字母和数字'
          }
          return true
        }
      }
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5199/bind',
    validateEndpoint: '/api/games/5199/validate'
  },
  customConfig: {
    supportTabs: true,
    tabs: [
      {
        label: '官服&IOS',
        type: BindingType.API,
        fields: [] // 使用密钥绑定，无需表单字段
      },
      {
        label: '其他渠道服',
        type: BindingType.API,
        fields: [
          {
            model: 'roleName',
            label: '角色昵称',
            placeholder: '请输入游戏中的角色昵称',
            type: FieldType.TEXT,
            required: true
          },
          {
            model: 'gameUid',
            label: '游戏UID',
            placeholder: '请输入游戏中的UID',
            type: FieldType.TEXT,
            required: true
          }
        ]
      }
    ],
    downloadUrl: 'https://www.5199.com/download',
    instructions: [
      '目前提供【官服】【TAPTAP】【好游快爆】【IOS平台】移动端的角色绑定操作:点击口令自动复制,手机打开《月圆之夜》最新版本游戏客户端,游戏将会自动验证你的口令信息。',
      '如进入游戏没有任何反应,请尝试:1)系统设置允许游戏读取剪切板;2)小退游戏重新进入',
      '如果无法绑定或绑定失败,请到游戏内找到"设置"-"客服帮助"尝试进行绑定。'
    ]
  }
}

// 球球大作战 (5078) - 短信验证绑定
export const game5078Config: GameBindingConfig = {
  gameId: '5078',
  gameName: '球球大作战',
  logoUrl: '/images/games/5078-logo.png',
  bindingType: BindingType.SMS,
  formFields: [
    {
      model: 'phoneNumber',
      label: '手机号',
      placeholder: '请输入手机号',
      type: FieldType.PHONE,
      required: true,
      validation: {
        pattern: '^1[3-9]\\d{9}$',
        validator: (value: string) => {
          if (!/^1[3-9]\d{9}$/.test(value)) {
            return '请输入正确的手机号码'
          }
          return true
        }
      }
    },
    {
      model: 'verificationCode',
      label: '验证码',
      placeholder: '请输入短信验证码',
      type: FieldType.VERIFICATION_CODE,
      required: true,
      hasCustomButton: true,
      customButton: {
        text: '获取验证码',
        action: 'sendVerificationCode'
      },
      validation: {
        minLength: 4,
        maxLength: 6,
        pattern: '^\\d+$'
      }
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5078/bind',
    sendCodeEndpoint: '/api/games/5078/sendCode'
  }
}

// 太空杀 (5256) - 短信验证绑定
export const game5256Config: GameBindingConfig = {
  gameId: '5256',
  gameName: '太空杀',
  logoUrl: '/images/games/5256-logo.png',
  bindingType: BindingType.SMS,
  formFields: [
    {
      model: 'phoneNumber',
      label: '手机号',
      placeholder: '请输入手机号',
      type: FieldType.PHONE,
      required: true,
      validation: {
        pattern: '^1[3-9]\\d{9}$'
      }
    },
    {
      model: 'verificationCode',
      label: '验证码',
      placeholder: '请输入短信验证码',
      type: FieldType.VERIFICATION_CODE,
      required: true,
      hasCustomButton: true,
      customButton: {
        text: '获取验证码',
        action: 'sendVerificationCode'
      }
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5256/bind',
    sendCodeEndpoint: '/api/games/5256/sendCode'
  }
}

// 街篮2 (5218) - 基础API绑定
export const game5218Config: GameBindingConfig = {
  gameId: '5218',
  gameName: '街篮2',
  logoUrl: '/images/games/5218-logo.png',
  bindingType: BindingType.API,
  formFields: [
    {
      model: 'roleName',
      label: '角色昵称',
      placeholder: '请输入游戏中的角色昵称',
      type: FieldType.TEXT,
      required: true
    },
    {
      model: 'gameId',
      label: '游戏ID',
      placeholder: '请输入游戏ID',
      type: FieldType.TEXT,
      required: true
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5218/bind'
  }
}

// 五千年 (5286) - 带服务器选择的API绑定
export const game5286Config: GameBindingConfig = {
  gameId: '5286',
  gameName: '五千年',
  logoUrl: '/images/games/5286-logo.png',
  bindingType: BindingType.API,
  formFields: [
    {
      model: 'customInput',
      label: '输入数据',
      placeholder: '请输入格式为"游戏昵称|游戏ID"的数据',
      type: FieldType.TEXT,
      required: true,
      hasCustomButton: true,
      customButton: {
        text: '快速填入',
        action: 'splitInputContent'
      }
    },
    {
      model: 'nickname',
      label: '游戏昵称',
      placeholder: '游戏昵称将自动填入',
      type: FieldType.TEXT,
      required: true
    },
    {
      model: 'gameId',
      label: '游戏ID',
      placeholder: '游戏ID将自动填入',
      type: FieldType.TEXT,
      required: true
    },
    {
      model: 'server',
      label: '选择服务器',
      placeholder: '请选择服务器',
      type: FieldType.SELECT,
      required: true,
      options: [
        { value: 'server1', label: '服务器1' },
        { value: 'server2', label: '服务器2' },
        { value: 'server3', label: '服务器3' },
        { value: 'server4', label: '服务器4' }
      ]
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5286/bind'
  }
}

// 游戏配置映射表
export const gameBindingConfigs: Record<string, GameBindingConfig> = {
  '5199': game5199Config,
  '5078': game5078Config,
  '5256': game5256Config,
  '5218': game5218Config,
  '5286': game5286Config
}

/**
 * 根据游戏ID获取绑定配置
 */
export function getGameBindingConfig(gameId: string): GameBindingConfig | null {
  return gameBindingConfigs[gameId] || null
}

/**
 * 获取所有支持的游戏ID列表
 */
export function getSupportedGameIds(): string[] {
  return Object.keys(gameBindingConfigs)
}
