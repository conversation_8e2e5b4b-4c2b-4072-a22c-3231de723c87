import { userApi } from './user'
import { gameApi } from './game'
import { homeApi } from './home'
import { bindingApi } from './game/binding'
import { configApi } from './config'
// 导入其他模块的API...

// 导出所有API模块
export const api = {
  user: userApi,
  game: gameApi,
  home: homeApi,
  binding: bindingApi,
  config: configApi
} as const

// 导出单独的API模块，方便直接导入使用
export { 
  userApi, 
  gameApi, 
  homeApi, 
  bindingApi,
  configApi
}
