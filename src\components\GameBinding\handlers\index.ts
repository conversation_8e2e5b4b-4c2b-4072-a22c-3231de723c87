/**
 * 绑定处理器工厂
 */

import { BindingType, BindingHandler } from '@/types/game-binding'
import { ApiBindingHandler } from './ApiBindingHandler'
import { SmsBindingHandler } from './SmsBindingHandler'
import { OAuthBindingHandler } from './OAuthBindingHandler'

// 处理器实例缓存
const handlerCache = new Map<BindingType, BindingHandler>()

/**
 * 获取绑定处理器
 */
export function getBindingHandler(type: BindingType): BindingHandler {
  // 从缓存中获取
  if (handlerCache.has(type)) {
    return handlerCache.get(type)!
  }

  // 创建新的处理器实例
  let handler: BindingHandler

  switch (type) {
    case BindingType.API:
      handler = new ApiBindingHandler()
      break
    case BindingType.SMS:
      handler = new SmsBindingHandler()
      break
    case BindingType.OAUTH:
      handler = new OAuthBindingHandler()
      break
    default:
      throw new Error(`不支持的绑定类型: ${type}`)
  }

  // 缓存处理器实例
  handlerCache.set(type, handler)
  return handler
}

/**
 * 清理处理器缓存
 */
export function clearHandlerCache() {
  // 清理SMS处理器的定时器等资源
  const smsHandler = handlerCache.get(BindingType.SMS) as SmsBindingHandler
  if (smsHandler && typeof smsHandler.destroy === 'function') {
    smsHandler.destroy()
  }

  handlerCache.clear()
}

// 导出所有处理器类
export { ApiBindingHandler } from './ApiBindingHandler'
export { SmsBindingHandler } from './SmsBindingHandler'
export { OAuthBindingHandler } from './OAuthBindingHandler'
export { BaseBindingHandler } from './BaseBindingHandler'
