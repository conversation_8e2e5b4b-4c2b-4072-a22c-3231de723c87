import { defineStore } from 'pinia'

// 主题类型定义
export interface ThemeConfig {
  background: string
  logo: string
  banners: Array<{id: number, imageUrl: string, jumpUrl?: string, title?: string}>
}

// 首页配置类型
export interface HomeConfig {
  defaultTheme: ThemeConfig
  gameThemes: Record<number, ThemeConfig>
}

export const useThemeStore = defineStore('theme', {
  state: () => ({
    // 主页配置
    homeConfig: null as HomeConfig | null,
    // 当前激活主题ID，与游戏ID对应
    activeThemeId: 0
  }),

  getters: {
    /**
     * 根据游戏ID获取主题
     */
    getThemeByGameId: (state) => (gameId: number) => {
      if (state.homeConfig && state.homeConfig.gameThemes[gameId]) {
        return state.homeConfig.gameThemes[gameId]
      }
      return state.homeConfig?.defaultTheme || {
        background: '',
        logo: '',
        banners: []
      }
    },

    /**
     * 获取当前激活的主题（兼容旧代码）
     */
    currentTheme(): ThemeConfig {
      if (this.homeConfig && this.activeThemeId && this.homeConfig.gameThemes[this.activeThemeId]) {
        return this.homeConfig.gameThemes[this.activeThemeId]
      }

      return this.homeConfig?.defaultTheme || {
        background: '',
        logo: '',
        banners: []
      }
    },

    /**
     * 检查是否有主题配置
     */
    hasThemeConfig(): boolean {
      return this.homeConfig !== null
    }
  },

  actions: {
    /**
     * 设置首页配置
     */
    setHomeConfig(config: HomeConfig) {
      this.homeConfig = config
      console.log('[ThemeStore] 设置主题配置成功')
    },

    /**
     * 设置当前主题ID
     */
    setActiveTheme(themeId: number) {
      this.activeThemeId = Number(themeId)
      console.log(`[ThemeStore] 切换到主题: ${themeId}`)
    },

    /**
     * 清除主题配置
     */
    clearThemeConfig() {
      this.homeConfig = null
      this.activeThemeId = 0
      console.log('[ThemeStore] 清除主题配置')
    }
  },

  persist: {
    enabled: true,
    storage: localStorage,
    key: '__giant__theme__config__',
  },
})