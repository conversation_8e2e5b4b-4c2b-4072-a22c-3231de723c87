<script setup lang="ts">
import { ref, reactive } from 'vue'
import BindForm from '@/components/BindForm/index.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const bindFormRef = ref()

// 游戏信息
const gameInfo = reactive({
	id: route.query.gameId as string || '',
	name: route.query.gameName as string || '游戏名称',
	logo: route.query.logo as string || '/images/default-game-logo.png'
})

// 表单字段定义 - 只需要角色昵称和游戏ID
const formFields = [
	{
		model: 'roleName',
		label: '角色昵称',
		placeholder: '请输入角色昵称',
		required: true
	},
	{
		model: 'gameId',
		label: '游戏ID',
		placeholder: '请输入游戏ID',
		required: true
	}
]

// 额外数据
const additionalData = {
	gameId: gameInfo.id
}

// 绑定游戏账号
async function bindGameAccount(formData: Record<string, any>) {
	try {
		// 这里调用实际的绑定API
		// const response = await api.bindGameAccount({
		//   gameId: gameInfo.id,
		//   roleName: formData.roleName,
		//   gameId: formData.gameId
		// })
		
		console.log('绑定游戏账号:', formData)
		
		// 模拟返回数据
		return {
			game_id: gameInfo.id,
			character_name: formData.roleName,
			account_uid: formData.gameId
		}
	}
	catch (error) {
		console.error('绑定失败:', error)
		return null
	}
}
</script>

<template>
	<div class="h-full w-full bg-gray-50">
		<BindForm
			:logo-src="gameInfo.logo"
			:input-fields="formFields"
			:bind-function="bindGameAccount"
			:additional-data="additionalData"
			:custom-button-string="'绑定'"
			ref="bindFormRef"
		>
		</BindForm>
	</div>
</template>

<style scoped>
/* 所有样式已使用UnoCSS原子类替代 */
</style>

<route lang="json">
{
	"meta": {
		"title": "角色绑定",
		"layout": "backHeader"
	}
}
</route>


