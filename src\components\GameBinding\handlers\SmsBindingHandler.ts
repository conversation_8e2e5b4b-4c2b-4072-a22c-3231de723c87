/**
 * 短信验证绑定处理器
 * 处理通过手机号和验证码进行的游戏账号绑定
 */

import { 
  BindingType, 
  BindingParams, 
  BindingResult, 
  GameBindingConfig,
  VerificationCodeState
} from '@/types/game-binding'
import { BaseBindingHandler } from './BaseBindingHandler'
import { ref } from 'vue'

export class SmsBindingHandler extends BaseBindingHandler {
  type = BindingType.SMS

  // 验证码状态管理
  private verificationState = ref<VerificationCodeState>({
    isCountingDown: false,
    countdown: 60
  })

  /**
   * 执行短信验证绑定
   */
  async bind(params: BindingParams, config: GameBindingConfig): Promise<BindingResult> {
    const { gameId, formData } = params

    try {
      // 验证表单数据
      await this.validateForm(formData, config)

      // 验证手机号格式
      if (!this.isValidPhoneNumber(formData.phoneNumber)) {
        return this.formatBindingResult(false, null, '请输入正确的手机号码')
      }

      // 验证验证码格式
      if (!this.isValidVerificationCode(formData.verificationCode)) {
        return this.formatBindingResult(false, null, '请输入正确的验证码')
      }

      // 准备绑定数据
      const bindData = {
        gameId,
        phoneNumber: formData.phoneNumber,
        verificationCode: formData.verificationCode
      }

      // 调用绑定API
      const response = await this.makeRequest<any>(
        'POST',
        config.apiConfig.bindEndpoint,
        bindData
      )

      // 处理响应
      if (response.success !== false) {
        return this.formatBindingResult(true, {
          gameId,
          phoneNumber: formData.phoneNumber,
          accountUid: response.accountId || formData.phoneNumber,
          bindingId: response.bindingId
        }, '绑定成功')
      } else {
        return this.formatBindingResult(false, null, response.message || '绑定失败')
      }

    } catch (error: any) {
      console.error('短信验证绑定失败:', error)
      return this.formatBindingResult(false, null, error.message || '绑定失败，请稍后重试')
    }
  }

  /**
   * 发送验证码
   */
  async sendVerificationCode(
    phoneNumber: string, 
    config: GameBindingConfig
  ): Promise<{ success: boolean; message?: string }> {
    try {
      // 检查是否正在倒计时
      if (this.verificationState.value.isCountingDown) {
        return {
          success: false,
          message: `请等待${this.verificationState.value.countdown}秒后重试`
        }
      }

      // 验证手机号
      if (!this.isValidPhoneNumber(phoneNumber)) {
        return {
          success: false,
          message: '请输入正确的手机号码'
        }
      }

      // 发送验证码
      const response = await this.makeRequest<any>(
        'POST',
        config.apiConfig.sendCodeEndpoint!,
        {
          phoneNumber,
          gameId: config.gameId
        }
      )

      if (response.success !== false) {
        // 开始倒计时
        this.startCountdown()
        return {
          success: true,
          message: '验证码已发送'
        }
      } else {
        return {
          success: false,
          message: response.message || '发送验证码失败'
        }
      }

    } catch (error: any) {
      console.error('发送验证码失败:', error)
      return {
        success: false,
        message: error.message || '发送验证码失败'
      }
    }
  }

  /**
   * 开始倒计时
   */
  private startCountdown() {
    this.verificationState.value.isCountingDown = true
    this.verificationState.value.countdown = 60

    this.verificationState.value.timerId = setInterval(() => {
      this.verificationState.value.countdown--
      
      if (this.verificationState.value.countdown <= 0) {
        this.stopCountdown()
      }
    }, 1000)
  }

  /**
   * 停止倒计时
   */
  private stopCountdown() {
    if (this.verificationState.value.timerId) {
      clearInterval(this.verificationState.value.timerId)
      this.verificationState.value.timerId = undefined
    }
    this.verificationState.value.isCountingDown = false
    this.verificationState.value.countdown = 60
  }

  /**
   * 验证手机号格式
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    return /^1[3-9]\d{9}$/.test(phoneNumber)
  }

  /**
   * 验证验证码格式
   */
  private isValidVerificationCode(code: string): boolean {
    return /^\d{4,6}$/.test(code)
  }

  /**
   * 获取验证码状态
   */
  getVerificationState() {
    return this.verificationState
  }

  /**
   * 获取自定义组件
   */
  getCustomComponents() {
    return {
      // 验证码按钮组件
      verificationCodeButton: {
        state: this.verificationState,
        sendCode: this.sendVerificationCode.bind(this)
      }
    }
  }

  /**
   * 清理资源
   */
  destroy() {
    this.stopCountdown()
  }
}
