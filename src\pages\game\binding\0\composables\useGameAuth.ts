import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useToast } from '@/composables/useToast'
// 引入新的API模块
import { useBindingService } from '@/api/game'

export type AuthParams = {
  token?: string
  user?: string
}

interface BindGameParams {
  gameId: string | string[]
  token: string
  user: string
}

/**
 * 游戏授权与绑定相关功能Hook
 */
export const useGameAuth = () => {
  const router = useRouter()
  const userStore = useUserStore()
  const toast = useToast()
  
  // 使用绑定API服务
  const { 
    bindingState,
    fetchBindCode,
    bindGame,
    bindCodeLoading,
    bindGameLoading
  } = useBindingService()
  
  // 绑定结果状态
  const bindingSuccess = ref(false)
  const bindingError = ref('')
  const isBinding = ref(false)
  
  // 用户Token和信息
  const token = ref<string>('')
  const user = reactive({
    id: '',
    nickname: '',
    avatar: ''
  })
  
  /**
   * 重定向到巨人授权
   */
  const redirectToGiantAuth = (gameId: string | number) => {
    // 跳转到授权页面逻辑...
    // 实际实现中需要获取授权URL并跳转
    console.log('重定向到授权页面，游戏ID:', gameId)
    
    // 模拟授权流程
    setTimeout(() => {
      // 授权成功后返回并携带token
      token.value = `auth_token_${Date.now()}`
      checkAndHandleAuth()
    }, 1000)
  }
  
  /**
   * 通过其他渠道绑定
   */
  const bindWithOtherChannel = async (gameId: string | number, uid: string) => {
    isBinding.value = true
    
    try {
      // 获取绑定验证码
      await fetchBindCode({ gameId: Number(gameId) })
      
      // 执行绑定操作
      if (bindingState.value.currentCode) {
        bindingState.value.currentUid = uid
        
        const result = await bindGame({
          gameId: Number(gameId),
          uid: uid,
          bindCode: bindingState.value.currentCode
        })
        
        // 处理绑定结果
        if (result?.data) {
          bindingSuccess.value = true
          bindingError.value = ''
          toast.success('游戏账号绑定成功')
        } else {
          bindingError.value = '绑定失败，请重试'
          toast.error('绑定失败，请重试')
        }
      }
    } catch (error) {
      bindingError.value = '绑定过程中发生错误'
      toast.error('绑定过程中发生错误')
      console.error('绑定错误:', error)
    } finally {
      isBinding.value = false
    }
  }
  
  /**
   * 获取游戏服务器列表
   */
  const fetchGameServers = async (gameId: string | number) => {
    // 这里可以调用API获取服务器列表
    return [
      { label: '服务器1', value: 'server1' },
      { label: '服务器2', value: 'server2' },
      { label: '服务器3', value: 'server3' }
    ]
  }
  
  /**
   * 检查并处理授权结果
   */
  const checkAndHandleAuth = () => {
    if (token.value) {
      // 模拟用户信息
      user.id = 'user_' + Math.floor(Math.random() * 10000)
      user.nickname = '玩家' + user.id
      user.avatar = 'https://img.example.com/avatar.jpg'
      
      return true
    }
    return false
  }
  
  return {
    redirectToGiantAuth,
    bindWithOtherChannel,
    fetchGameServers,
    checkAndHandleAuth,
    bindingSuccess,
    bindingError,
    isBinding: computed(() => isBinding.value || bindCodeLoading || bindGameLoading),
    token,
    user
  }
} 