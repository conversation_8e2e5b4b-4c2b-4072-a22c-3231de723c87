<script setup lang="ts">
import { ref, reactive } from 'vue'
import BindForm from '@/components/BindForm/index.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const bindFormRef = ref()

// 游戏信息
const gameInfo = reactive({
	id: route.query.gameId as string || '',
	name: route.query.gameName as string || '游戏名称',
	logo: route.query.logo as string || '/images/default-game-logo.png'
})

// 表单字段定义
const formFields = [
	{
		model: 'customInput',
		label: '输入数据',
		placeholder: '请输入格式为"游戏昵称|游戏ID"的数据',
		required: true
	},
	{
		model: 'nickname',
		label: '游戏昵称',
		placeholder: '游戏昵称将自动填入',
		required: true
	},
	{
		model: 'gameId',
		label: '游戏ID',
		placeholder: '游戏ID将自动填入',
		required: true
	},
	{
		model: 'server',
		label: '选择服务器',
		placeholder: '请选择服务器',
		type: 'select',
		options: [
			{ value: 'server1', label: '服务器1' },
			{ value: 'server2', label: '服务器2' },
			{ value: 'server3', label: '服务器3' },
			{ value: 'server4', label: '服务器4' }
		],
		required: true
	}
]

// 额外数据
const additionalData = {
	gameId: gameInfo.id
}

// 分割输入内容函数
function splitInputContent() {
	const customInput = bindFormRef.value?.data.FormData.customInput
	
	if (!customInput) {
		console.error('请先输入需要分割的数据')
		return
	}
	
	// 使用"|"分割输入内容
	const parts = customInput.split('|')
	
	if (parts.length >= 2) {
		// 将分割后的内容填入对应字段
		bindFormRef.value.data.FormData.nickname = parts[0].trim()
		bindFormRef.value.data.FormData.gameId = parts[1].trim()
	} else {
		console.error('输入格式错误，无法分割')
	}
}

// 绑定游戏账号
async function bindGameAccount(formData: Record<string, any>) {
	try {
		// 这里调用实际的绑定API
		// const response = await api.bindGameAccount({
		//   gameId: gameInfo.id,
		//   nickname: formData.nickname,
		//   gameId: formData.gameId,
		//   server: formData.server
		// })
		
		console.log('绑定游戏账号:', formData)
		
		// 模拟返回数据
		return {
			game_id: gameInfo.id,
			character_name: formData.nickname,
			account_uid: formData.gameId
		}
	}
	catch (error) {
		console.error('绑定失败:', error)
		return null
	}
}
</script>

<template>
	<div class="h-full w-full bg-gray-50">
		<BindForm
			:logo-src="gameInfo.logo"
			:input-fields="formFields"
			:bind-function="bindGameAccount"
			:additional-data="additionalData"
			:custom-button-string="'绑定'"
			ref="bindFormRef"
		>
			<!-- 使用命名插槽为自定义功能输入框添加分割按钮 -->
			<template #custom-button-customInput>
				<div class="absolute right-0 top-0 h-full flex items-center z-10">
					<var-button
						class="w-24 h-[40px] border-none rounded-none rounded-r-4 whitespace-nowrap"
						:class="'bg-[#FFD722] text-[#333333] font-medium'"
						@click.prevent="splitInputContent"
						:elevation="false"
						text
					>
						快速填入
					</var-button>
				</div>
			</template>
		</BindForm>
	</div>
</template>

<style scoped>
/* 所有样式已使用UnoCSS原子类替代 */
</style>

<route lang="json">
{
	"meta": {
		"title": "游戏账号绑定",
		"layout": "backHeader"
	}
}
</route>


