import { ref, onMounted } from 'vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { binding5199Api, mockBinding5199Api } from '../api'
import { generateMockBindKey } from '@/api/game/mock'
import { withMockData } from '@/utils/mock'

/**
 * 用于获取5199游戏绑定密钥的组合式函数
 * @returns 包含密钥信息和获取方法的对象
 */
export function useBindKey() {
  // 存储密钥的响应式引用
  const bindKey = ref('')
  
  // 获取绑定密钥的请求
  const { 
    loading, 
    error,
    run: fetchKey
  } = useApiRequest(
    () => binding5199Api.getBindKey(),
    {
      manual: true,
      onSuccess: (data) => {
        bindKey.value = data.key
      },
      onError: (err) => {
        console.error('获取绑定密钥失败:', err)
      }
    },
    generateMockBindKey('5199') // 提供模拟数据
  )

  /**
   * 获取绑定密钥
   */
  const fetchBindKey = async () => {
    try {
      await fetchKey()
      return bindKey.value
    } catch (err) {
      console.error('获取绑定密钥失败:', err)
      return null
    }
  }
  
  // 首次挂载时自动获取密钥
  onMounted(fetchBindKey)

  return {
    bindKey,
    loading,
    error,
    fetchBindKey
  }
} 