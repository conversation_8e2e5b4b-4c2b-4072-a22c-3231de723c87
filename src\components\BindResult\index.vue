<script setup lang="ts">

const props = defineProps<{
  success: boolean
  message?: string
  errorMessage?: string
  buttonText?: string
  showButton?: boolean
}>()

const emit = defineEmits<{
  (e: 'confirm'): void
  (e: 'retry'): void
}>()

// 确认按钮点击事件
function handleConfirm() {
  emit('confirm')
}

// 重试按钮点击事件
function handleRetry() {
  emit('retry')
}
</script>

<template>
  <div class="flex flex-col items-center justify-center p-8 w-full">
    <var-icon 
      :name="success ? 'checkbox-marked-circle-outline' : 'alert-circle-outline'" 
      :class="success ? 'text-[#4CAF50]' : 'text-[#F44336]'"
      size="68"
    />
    
    <div class="text-xl font-medium mt-4 text-center">
      {{ success ? (message || '绑定成功') : (errorMessage || '绑定失败') }}
    </div>
    
    <p v-if="!success" class="text-[#666] text-sm mt-2 text-center">
      请稍后重试或联系客服解决
    </p>
    
    <var-button
      v-if="showButton !== false"
      class="mt-8 h-[48px] w-full rounded-2xl bg-[#FFD722] text-[#333] font-medium text-base border-none"
      :elevation="false"
      text
      @click="success ? handleConfirm() : handleRetry()"
    >
      {{ buttonText || (success ? '确定' : '重试') }}
    </var-button>
  </div>
</template> 