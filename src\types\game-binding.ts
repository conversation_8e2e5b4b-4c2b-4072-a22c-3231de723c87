/**
 * 游戏绑定相关类型定义
 */

// 绑定类型枚举
export enum BindingType {
  /** API绑定：通过UID和昵称绑定 */
  API = 'api',
  /** 短信验证绑定：通过手机号和验证码绑定 */
  SMS = 'sms',
  /** OAuth重定向绑定：跳转到游戏官方登录页面 */
  OAUTH = 'oauth'
}

// 表单字段类型
export enum FieldType {
  /** 文本输入框 */
  TEXT = 'text',
  /** 选择器 */
  SELECT = 'select',
  /** 手机号输入框 */
  PHONE = 'phone',
  /** 验证码输入框 */
  VERIFICATION_CODE = 'verification_code'
}

// 选择器选项
export interface SelectOption {
  value: string
  label: string
}

// 表单字段配置
export interface FormFieldConfig {
  /** 字段标识符 */
  model: string
  /** 字段标签 */
  label: string
  /** 占位符文本 */
  placeholder: string
  /** 字段类型 */
  type: FieldType
  /** 是否必填 */
  required?: boolean
  /** 选择器选项（仅当type为SELECT时使用） */
  options?: SelectOption[]
  /** 字段验证规则 */
  validation?: {
    /** 最小长度 */
    minLength?: number
    /** 最大长度 */
    maxLength?: number
    /** 正则表达式验证 */
    pattern?: string
    /** 自定义验证函数 */
    validator?: (value: string) => boolean | string
  }
  /** 是否支持自定义按钮（如验证码获取按钮） */
  hasCustomButton?: boolean
  /** 自定义按钮配置 */
  customButton?: {
    text: string
    action: string
  }
}

// API配置
export interface ApiConfig {
  /** 绑定API端点 */
  bindEndpoint: string
  /** 验证API端点（可选） */
  validateEndpoint?: string
  /** 获取服务器列表API端点（可选） */
  serverListEndpoint?: string
  /** 发送验证码API端点（仅SMS类型使用） */
  sendCodeEndpoint?: string
}

// OAuth配置
export interface OAuthConfig {
  /** 授权URL */
  authUrl: string
  /** 回调URL */
  callbackUrl: string
  /** 客户端ID */
  clientId: string
  /** 授权范围 */
  scope?: string
}

// 游戏绑定配置
export interface GameBindingConfig {
  /** 游戏ID */
  gameId: string
  /** 游戏名称 */
  gameName: string
  /** 游戏Logo URL */
  logoUrl: string
  /** 绑定类型 */
  bindingType: BindingType
  /** 表单字段配置 */
  formFields: FormFieldConfig[]
  /** API配置 */
  apiConfig: ApiConfig
  /** OAuth配置（仅当bindingType为OAUTH时使用） */
  oauthConfig?: OAuthConfig
  /** 自定义配置 */
  customConfig?: {
    /** 是否支持多标签页 */
    supportTabs?: boolean
    /** 标签页配置 */
    tabs?: Array<{
      label: string
      type: BindingType
      fields: FormFieldConfig[]
    }>
    /** 下载链接 */
    downloadUrl?: string
    /** 绑定说明 */
    instructions?: string[]
  }
}

// 绑定请求参数
export interface BindingParams {
  gameId: string
  formData: Record<string, any>
  additionalData?: Record<string, any>
}

// 绑定响应结果
export interface BindingResult {
  success: boolean
  message?: string
  data?: {
    gameId: string
    characterName?: string
    accountUid?: string
    bindingId?: string
    [key: string]: any
  }
}

// 验证码状态
export interface VerificationCodeState {
  /** 是否正在倒计时 */
  isCountingDown: boolean
  /** 剩余秒数 */
  countdown: number
  /** 计时器ID */
  timerId?: NodeJS.Timeout
}

// 绑定处理器接口
export interface BindingHandler {
  /** 处理器类型 */
  type: BindingType
  /** 验证表单数据 */
  validateForm(formData: Record<string, any>, config: GameBindingConfig): Promise<boolean>
  /** 执行绑定 */
  bind(params: BindingParams, config: GameBindingConfig): Promise<BindingResult>
  /** 获取额外的UI组件（如验证码按钮） */
  getCustomComponents?(): Record<string, any>
}

// 服务器信息
export interface ServerInfo {
  id: string
  name: string
  region?: string
}

// 验证结果
export interface ValidationResult {
  valid: boolean
  message?: string
}
