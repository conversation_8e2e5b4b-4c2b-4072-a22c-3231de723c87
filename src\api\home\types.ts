import type { Game, GameAccount } from '@/stores/game'
import type { ThemeConfig } from '@/stores/theme'

// 首页主题配置请求参数
export interface HomeConfigParams {
  gameId?: number
}

// 首页主题配置响应
export interface HomeConfigResponse {
  appBarImage: string
  logo: string
  banners: Array<{
    id: number
    imageUrl: string
    jumpUrl?: string
    title?: string
  }>
  services: Array<{
    id: number
    title: string
    description: string
    icon?: string
    color?: string
  }>
}

// 游戏数据请求参数
export interface GameDataParams {
  uid?: string
}

// 游戏数据响应
export interface GameDataResponse {
  games: Game[]
  accounts: GameAccount[]
}

// 账户操作请求参数
export interface AccountOperationParams {
  accountId: string
  gameId: number
}

// 设置账户为激活状态的参数
export interface SetActiveAccountParams {
  accountId: string
  gameId: number
}

// 绑定账户请求参数
export interface BindAccountParams {
  gameId: number
  uid: string
  bindKey: string
}

// 解绑账户请求参数
export interface UnbindAccountParams {
  accountId: string
}

// 基础响应类型
export interface BaseResponse<T> {
  code: number
  message: string
  data: T
}

// 用户游戏绑定信息
export interface UserBindingInfo {
  uid: string // 游戏角色 UID
  name: string // 游戏内角色名
  avatar: string // 游戏角色头像
  gameId: string // 游戏 ID
  gameName: string // 游戏名称
  isActive: boolean // 是否为当前激活游戏
  bindingId: number // 绑定记录 ID
}

// 获取用户绑定信息响应
export interface GetUserBindingsResponse extends BaseResponse<UserBindingInfo[]> {}

// 解绑游戏请求参数
export interface UnbindGameParams {
  bindingId: number
}

// 切换激活游戏请求参数
export interface SwitchGameParams {
  bindingId: number
} 