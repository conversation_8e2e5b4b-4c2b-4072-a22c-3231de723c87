import { request } from '@/utils/http'
import type { 
  GetBindCodeParams, 
  BindCodeResponse, 
  BindGameParams,
  BindHistoryParams,
  BindHistoryResponse,
  BaseResponse
} from './types'

/**
 * 游戏绑定相关API定义
 */
export const bindingApi = {
  // 获取绑定验证码
  getBindCode: (params: GetBindCodeParams) => 
    request<BaseResponse<BindCodeResponse>>({
      url: '/api/game/bind/code',
      method: 'GET',
      params,
    }),
  
  // 绑定游戏账号
  bindGame: (params: BindGameParams) => 
    request<BaseResponse<boolean>>({
      url: '/api/game/bind',
      method: 'POST',
      data: params,
    }),
  
  // 获取绑定历史
  getBindHistory: (params?: BindHistoryParams) => 
    request<BaseResponse<BindHistoryResponse>>({
      url: '/api/game/bind/history',
      method: 'GET',
      params,
    }),
} 