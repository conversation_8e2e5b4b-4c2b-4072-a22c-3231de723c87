import { defineStore } from 'pinia'
import { homeApi } from '@/api'

// 游戏信息
export interface Game {
  gameId: number
  name: string
  iconLogo: string // 方形 Logo
  wideLogo: string // 长方形 Logo
  avatar: string
}

// 用户绑定的角色信息
export interface UserBinding {
  uid: string
  name: string
  avatar: string
  gameId: number
  gameName: string
  isActive: boolean
  bindingId: number
}

export const useGameStore = defineStore('game', {
  state: () => ({
    // 游戏列表
    games: [] as Game[],
    // 用户绑定的角色列表
    bindings: [] as UserBinding[],
    // 当前激活的角色
    activeBinding: null as UserBinding | null,
  }),

  getters: {
    // 当前激活的游戏
    activeGame(): Game | null {
      if (!this.activeBinding) return null
      return this.games.find(game => game.id === this.activeBinding!.gameId) || null
    },

    // 获取某游戏下的所有角色
    getBindingsByGameId: (state) => (gameId: number) => {
      return state.bindings.filter(binding => binding.gameId === gameId)
    },

    // 已绑定的游戏列表（去重）
    boundGames(): Game[] {
      const boundGameIds = new Set(this.bindings.map(b => b.gameId))
      return this.games.filter(game => boundGameIds.has(game.id))
    },

    // 是否有绑定的角色
    hasBindings(): boolean {
      return this.bindings.length > 0
    }
  },

  actions: {
    /**
     * 设置游戏列表
     */
    setGames(games: Game[]) {
      this.games = games
      console.log(`[GameStore] 设置游戏列表: ${games.length} 个游戏`)
    },

    /**
     * 设置用户绑定列表
     */
    setBindings(bindings: UserBinding[]) {
      this.bindings = bindings
      console.log(`[GameStore] 设置绑定列表: ${bindings.length} 个角色`)

      // 设置当前激活角色
      const activeBinding = bindings.find(b => b.isActive)
      if (activeBinding) {
        this.activeBinding = activeBinding
        console.log(`[GameStore] 激活角色: ${activeBinding.name} (${activeBinding.gameName})`)
      } else if (bindings.length > 0) {
        // 如果没有激活角色，选择第一个
        this.setActiveBinding(bindings[0])
      }
    },

    /**
     * 设置激活角色
     */
    async setActiveBinding(binding: UserBinding) {
      try {
        // 调用API切换激活角色
        await homeApi.switchGame({ bindingId: binding.bindingId })

        // 更新本地状态
        this.bindings.forEach(b => b.isActive = false)
        binding.isActive = true
        this.activeBinding = binding

        console.log(`[GameStore] 切换激活角色: ${binding.name} (${binding.gameName})`)
      } catch (error) {
        console.error('[GameStore] 切换角色失败:', error)
        throw error
      }
    },
    /**
     * 添加新绑定
     */
    addBinding(binding: UserBinding) {
      // 检查是否已存在
      const existingIndex = this.bindings.findIndex(b => b.uid === binding.uid && b.gameId === binding.gameId)

      if (existingIndex >= 0) {
        this.bindings[existingIndex] = binding
      } else {
        this.bindings.push(binding)
      }

      console.log(`[GameStore] 添加绑定: ${binding.name} (${binding.gameName})`)
    },

    /**
     * 解绑角色
     */
    async removeBinding(bindingId: number) {
      try {
        // 调用API解绑
        await homeApi.unbindGame({ bindingId })

        // 重新获取绑定列表
        await this.refreshBindings()

        console.log(`[GameStore] 解绑成功: ${bindingId}`)
      } catch (error) {
        console.error('[GameStore] 解绑失败:', error)
        throw error
      }
    },

    /**
     * 刷新绑定列表
     */
    async refreshBindings() {
      try {
        const response = await homeApi.getUserBindings()
        const bindings = response.data.map((item: any) => ({
          uid: item.uid,
          name: item.name,
          avatar: item.avatar,
          gameId: Number(item.gameId),
          gameName: item.gameName,
          isActive: item.isActive,
          bindingId: item.bindingId
        }))

        this.setBindings(bindings)
      } catch (error) {
        console.error('[GameStore] 刷新绑定列表失败:', error)
        throw error
      }
    },

    /**
     * 清除所有数据
     */
    clear() {
      this.games = []
      this.bindings = []
      this.activeBinding = null
      console.log('[GameStore] 清除所有数据')
    }
  },

  persist: {
    enabled: true,
    storage: localStorage,
    key: '__giant__game__store__',
  },
}) 