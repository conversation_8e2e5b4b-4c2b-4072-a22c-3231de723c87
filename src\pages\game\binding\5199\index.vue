<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useBindKey } from './composables/useBindKey'
import { useRouter } from 'vue-router'
import { useClipboard } from '@vueuse/core'
import { Tabs, Tab } from '@varlet/ui'

const router = useRouter()
// 标签页激活状态
const activeTab = ref(0)

// 使用绑定密钥组合式函数
const { bindKey, loading, error, fetchBindKey } = useBindKey()

// 使用VueUse的clipboard
const { copy, copied } = useClipboard()

// 切换标签时的处理
const handleTabChange = async (index: number) => {
  activeTab.value = index
  if (index === 0 && !bindKey.value) {
    await fetchBindKey()
  }
}

// 复制密钥到剪贴板
const handleCopyKey = async () => {
  if (!bindKey.value) {
    return
  }
  
  await copy(bindKey.value)
  if (copied.value) {
  }
}

// 立即下载游戏
const downloadGame = () => {
  window.open('https://www.5199.com/download', '_blank')
}

// 跳转到其他渠道服绑定页面
const goToBindPage = () => {
  router.push('/game/binding/5199/bind')
}

// 页面加载时，如果默认是官服IOS标签，则获取密钥
onMounted(async () => {
  if (activeTab.value === 0) {
    await fetchBindKey()
  }
})
</script>

<template>
  <div class="p-4 bg-gray-100 min-h-screen">
    <!-- 顶部标签页 -->
    <var-tabs
      v-model:active="activeTab"
      @change="handleTabChange"
      color="#F8F8F8"
      active-color="#FFD722"
      elevation="0"
    >
      <var-tab>官服&IOS</var-tab>
      <var-tab>其他渠道服</var-tab>
    </var-tabs>

    <!-- 内容区域 -->
    <div class="content bg-gray-200 rounded-xl p-4 mb-4 mt-4">
      <!-- 绑定账号标题 -->
      <h3 class="text-lg mb-3">绑定账号</h3>
      
      <!-- 官服&IOS内容 -->
      <template v-if="activeTab === 0">
        <div class="bg-black rounded-xl p-3 text-white mb-4">
          <p class="text-gray-400 text-sm mb-3">复制绑定口令，进入游戏完成绑定</p>
          <div class="flex items-center gap-3 cursor-pointer" @click="handleCopyKey">
            <i class="i-carbon:copy text-xl text-yellow"></i>
            <div class="text-yellow text-xl font-bold truncate w-full whitespace-nowrap overflow-hidden text-ellipsis">
              <template v-if="loading">正在获取密钥...</template>
              <template v-else-if="error">{{ error }}</template>
              <template v-else>{{ bindKey }}</template>
            </div>
          </div>
        </div>
      </template>

      <!-- 其他渠道服内容 -->
      <template v-else>
        <div class="bg-black rounded-xl p-4 text-center mb-4">
          <p class="text-gray-400 text-sm mb-4">若您不是【官服】【TAPTAP】【好游快爆】【IOS平台】移动端的角色</p>
          <button 
            class="w-full bg-yellow text-black font-medium py-2 px-8 rounded-xl"
            @click="goToBindPage"
          >
            点击绑定
          </button>
        </div>
      </template>

      <!-- 获取游戏 -->
      <h3 class="text-lg mb-3">获取游戏</h3>
      <button 
        class="w-full border border-black rounded-xl py-2 mb-4 font-medium"
        @click="downloadGame"
      >
        立即下载
      </button>

      <!-- 绑定说明 - 仅在官服&IOS标签下显示 -->
      <template v-if="activeTab === 0">
        <h3 class="text-lg mb-3">绑定说明</h3>
        <div class="bg-white rounded-xl p-4 text-sm text-gray-500">
          <ul class="list-disc pl-5 space-y-3">
            <li>目前提供【官服】【TAPTAP】【好游快爆】【IOS平台】移动端的角色绑定操作:点击口令自动复制,手机打开《月圆之夜》最新版本游戏客户端,游戏将会自动验证你的口令信息。</li>
            <li>如进入游戏没有任何反应,请尝试:1)系统设置允许游戏读取剪切板;2)小退游戏重新进入</li>
            <li>如果无法绑定或绑定失败,请到游戏内找到"设置"-"客服帮助"尝试进行绑定。</li>
          </ul>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.bg-yellow {
  background-color: #FFD722;
}

.text-yellow {
  color: #FFD722;
}

:deep(.var-tabs__tab) {
  flex: 1;
  border-radius: 9999px;
  transition: all 0.3s;
}

:deep(.var-tabs__tab--active) {
  font-weight: 500;
  color: #000 !important;
}

:deep(.var-tabs__indicator) {
  display: none;
}
</style>

<route lang="json">
{
    "meta": {
        "layout": "backHeader",
        "title": "5199账号绑定"
    }
}
</route>


