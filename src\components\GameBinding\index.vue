<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useClipboard } from '@vueuse/core'
import { Tabs, Tab } from '@varlet/ui'
import CustomSelect from '@/components/Select/index.vue'
import BindResult from '@/components/BindResult/index.vue'
import { useGameBinding } from '@/composables/useGameBinding'
import { FieldType, BindingType } from '@/types/game-binding'
import { getBindingHandler } from './handlers'

const router = useRouter()
const { copy, copied } = useClipboard()

// 使用游戏绑定组合式函数
const {
  loading,
  config,
  formData,
  currentTab,
  bindingResult,
  gameId,
  getCurrentFields,
  getCurrentBindingType,
  loadGameConfig,
  performBinding,
  handleCustomButtonClick,
  switchTab,
  validateForm
} = useGameBinding()

// 本地状态
const showResult = ref(false)
const bindKey = ref('')
const bindKeyLoading = ref(false)

// 计算属性
const isTabsSupported = computed(() => {
  return config.value?.customConfig?.supportTabs && config.value.customConfig.tabs
})

const currentFields = computed(() => getCurrentFields())
const currentBindingType = computed(() => getCurrentBindingType())

// 获取绑定密钥（5199游戏官服标签页）
const fetchBindKey = async () => {
  if (gameId.value !== '5199' || currentTab.value !== 0) return

  bindKeyLoading.value = true
  try {
    const handler = getBindingHandler(BindingType.API)
    const response = await (handler as any).makeRequest('GET', '/api/games/5199/bindKey')
    bindKey.value = response.bindCode || ''
  } catch (error: any) {
    console.error('获取绑定密钥失败:', error)
    bindKey.value = '获取失败，请重试'
  } finally {
    bindKeyLoading.value = false
  }
}

// 复制绑定密钥
const copyBindKey = async () => {
  if (!bindKey.value) return
  
  await copy(bindKey.value)
  if (copied.value) {
    // 可以显示复制成功的提示
  }
}

// 处理标签页切换
const handleTabChange = async (index: number) => {
  switchTab(index)
  
  // 如果切换到5199游戏的官服标签页，获取绑定密钥
  if (gameId.value === '5199' && index === 0) {
    await fetchBindKey()
  }
}

// 处理绑定按钮点击
const handleBindClick = async () => {
  // 验证表单
  const isValid = await validateForm()
  if (!isValid) return

  // 执行绑定
  const result = await performBinding()
  
  if (result) {
    showResult.value = true
  }
}

// 处理结果确认
const handleResultConfirm = () => {
  showResult.value = false
  // 可以添加跳转逻辑
}

// 处理结果重试
const handleResultRetry = () => {
  showResult.value = false
  // 清空表单或重置状态
}

// 下载游戏
const downloadGame = () => {
  if (config.value?.customConfig?.downloadUrl) {
    window.open(config.value.customConfig.downloadUrl, '_blank')
  }
}

// 跳转到其他绑定页面（5199游戏特殊逻辑）
const goToOtherChannelBind = () => {
  if (gameId.value === '5199') {
    router.push(`/game/binding/5199/bind?gameName=${config.value?.gameName}&logo=${config.value?.logoUrl}`)
  }
}

// 页面初始化
onMounted(async () => {
  const success = await loadGameConfig()
  if (!success) {
    router.push('/game/list')
    return
  }

  // 如果是5199游戏且默认在官服标签页，获取绑定密钥
  if (gameId.value === '5199' && currentTab.value === 0) {
    await fetchBindKey()
  }
})
</script>

<template>
  <div class="game-binding-container p-4 bg-gray-100 min-h-screen">
    <!-- 加载状态 -->
    <div v-if="!config" class="loading-container flex justify-center items-center py-12">
      <var-loading type="circle" color="#FFD722" size="large" />
    </div>

    <!-- 绑定结果展示 -->
    <BindResult 
      v-if="showResult && bindingResult"
      :success="bindingResult.success"
      :message="bindingResult.success ? '绑定成功' : undefined"
      :error-message="bindingResult.message"
      @confirm="handleResultConfirm"
      @retry="handleResultRetry"
    />

    <!-- 主要内容 -->
    <div v-else-if="config" class="binding-content">
      <!-- 标签页（如果支持） -->
      <var-tabs
        v-if="isTabsSupported"
        v-model:active="currentTab"
        @change="handleTabChange"
        color="#F8F8F8"
        active-color="#FFD722"
        elevation="0"
        class="mb-4"
      >
        <var-tab 
          v-for="(tab, index) in config.customConfig?.tabs" 
          :key="index"
        >
          {{ tab.label }}
        </var-tab>
      </var-tabs>

      <!-- 内容区域 -->
      <div class="content bg-gray-200 rounded-xl p-4 mb-4">
        <!-- 游戏Logo -->
        <div v-if="config.logoUrl" class="logo-container flex justify-center mb-4">
          <var-image :src="config.logoUrl" width="120" />
        </div>

        <!-- 绑定账号标题 -->
        <h3 class="text-lg mb-3">绑定账号</h3>
        
        <!-- 5199游戏官服标签页特殊处理 -->
        <template v-if="gameId === '5199' && currentTab === 0">
          <div class="bg-black rounded-xl p-3 text-white mb-4">
            <p class="text-gray-400 text-sm mb-3">复制绑定口令，进入游戏完成绑定</p>
            <div class="flex items-center gap-3 cursor-pointer" @click="copyBindKey">
              <i class="i-carbon:copy text-xl text-yellow"></i>
              <div class="text-yellow text-xl font-bold truncate w-full whitespace-nowrap overflow-hidden text-ellipsis">
                <template v-if="bindKeyLoading">正在获取密钥...</template>
                <template v-else>{{ bindKey || '点击获取密钥' }}</template>
              </div>
            </div>
          </div>
        </template>

        <!-- 5199游戏其他渠道标签页 -->
        <template v-else-if="gameId === '5199' && currentTab === 1">
          <div class="bg-black rounded-xl p-4 text-center mb-4">
            <p class="text-gray-400 text-sm mb-4">若您不是【官服】【TAPTAP】【好游快爆】【IOS平台】移动端的角色</p>
            <button 
              class="w-full bg-yellow text-black font-medium py-2 px-8 rounded-xl"
              @click="goToOtherChannelBind"
            >
              点击绑定
            </button>
          </div>
        </template>

        <!-- 通用表单 -->
        <template v-else-if="currentFields.length > 0">
          <div class="form-container">
            <div 
              v-for="field in currentFields" 
              :key="field.model" 
              class="field-container mb-4"
            >
              <label class="field-label text-base font-normal text-[#1C1C1C] block mb-2">
                {{ field.label }}
              </label>

              <!-- 选择器 -->
              <CustomSelect
                v-if="field.type === FieldType.SELECT"
                v-model="formData[field.model]"
                :options="field.options"
                :placeholder="field.placeholder"
              />

              <!-- 普通输入框 -->
              <div v-else class="input-container relative">
                <input
                  v-model="formData[field.model]"
                  :placeholder="field.placeholder"
                  :type="field.type === FieldType.PHONE ? 'tel' : 'text'"
                  class="w-full py-3 px-4 text-sm bg-[#EDEDED] rounded-lg border-none outline-none placeholder:text-[#B8B8B8]"
                >

                <!-- 自定义按钮（验证码、快速填入等） -->
                <div 
                  v-if="field.hasCustomButton && field.customButton"
                  class="absolute right-0 top-0 h-full flex items-center"
                >
                  <var-button
                    class="h-full px-4 border-none rounded-none rounded-r-lg whitespace-nowrap"
                    :class="field.type === FieldType.VERIFICATION_CODE ? 
                      'bg-[#FFD722] text-[#333333] font-medium' : 
                      'bg-[#FFD722] text-[#333333] font-medium'"
                    @click="handleCustomButtonClick(field.customButton!.action, field.model)"
                    :elevation="false"
                    text
                  >
                    {{ field.customButton.text }}
                  </var-button>
                </div>
              </div>
            </div>

            <!-- 绑定按钮 -->
            <var-button
              type="primary"
              class="w-full h-12 rounded-xl bg-[#FFD722] text-[#333] font-medium text-base border-none mt-4"
              :loading="loading"
              @click="handleBindClick"
              :elevation="false"
              text
            >
              {{ loading ? '绑定中...' : '绑定账号' }}
            </var-button>
          </div>
        </template>

        <!-- 获取游戏按钮 -->
        <template v-if="config.customConfig?.downloadUrl">
          <h3 class="text-lg mb-3 mt-6">获取游戏</h3>
          <button 
            class="w-full border border-black rounded-xl py-2 mb-4 font-medium"
            @click="downloadGame"
          >
            立即下载
          </button>
        </template>

        <!-- 绑定说明 -->
        <template v-if="config.customConfig?.instructions && gameId === '5199' && currentTab === 0">
          <h3 class="text-lg mb-3">绑定说明</h3>
          <div class="bg-white rounded-xl p-4 text-sm text-gray-500">
            <ul class="list-disc pl-5 space-y-3">
              <li v-for="(instruction, index) in config.customConfig.instructions" :key="index">
                {{ instruction }}
              </li>
            </ul>
          </div>
        </template>
      </div>
    </div>

    <!-- 底部客服链接 -->
    <div class="fixed bottom-4 left-0 right-0 flex items-center justify-center">
      <div class="text-sm text-[#B8B8B8]">
        遇到问题？点此
        <span class="text-[#FFD722] cursor-pointer" @click="router.push('/contact')">联系客服</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-yellow {
  background-color: #FFD722;
}

.text-yellow {
  color: #FFD722;
}

:deep(.var-tabs__tab) {
  flex: 1;
  border-radius: 9999px;
  transition: all 0.3s;
}

:deep(.var-tabs__tab--active) {
  font-weight: 500;
  color: #000 !important;
}

:deep(.var-tabs__indicator) {
  display: none;
}
</style>
