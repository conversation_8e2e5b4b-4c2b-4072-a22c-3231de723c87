import { useApiRequest } from '@/composables/useApiRequest'
import { ref, computed, onMounted } from 'vue'
import { bindingApi } from '@/api'
import type { 
  GetBindCodeParams, 
  BindGameParams,
  BindHistoryParams
} from '@/api/game/types'
import { useUserStore } from '@/stores/user'
import { useToast } from '@/composables/useToast'

/**
 * 游戏绑定相关Hook
 */
export function useBinding() {
  const userStore = useUserStore()
  const toast = useToast()
  
  // 绑定状态
  const bindingState = ref({
    currentCode: '',
    currentUid: '',
    expireTime: 0,
    qrCodeUrl: ''
  })
  
  // 获取绑定验证码
  const {
    data: bindCodeData,
    loading: bindCodeLoading,
    run: fetchBindCode
  } = useApiRequest(
    (params: GetBindCodeParams) => bindingApi.getBindCode(params),
    {
      manual: true,
      onSuccess: (response) => {
        if (response.data) {
          bindingState.value = {
            currentCode: response.data.bindCode,
            currentUid: '',
            expireTime: response.data.expireTime,
            qrCodeUrl: response.data?.qrCodeUrl || ''
          }
        }
      }
    }
  )
  
  // 执行游戏绑定
  const {
    data: bindGameData,
    loading: bindGameLoading,
    run: bindGame
  } = useApiRequest(
    (params: BindGameParams) => bindingApi.bindGame(params),
    {
      manual: true,
      onSuccess: (response) => {
        if (response.data) {
          toast.success('游戏账号绑定成功')
          // 刷新用户游戏信息
          userStore.refreshUserGames?.()
        } else {
          toast.error('绑定失败，请重试')
        }
      },
      onError: () => {
        toast.error('绑定失败，请稍后再试')
      }
    }
  )
  
  // 获取绑定历史
  const {
    data: bindHistoryData,
    loading: bindHistoryLoading,
    run: fetchBindHistory
  } = useApiRequest(
    (params?: BindHistoryParams) => bindingApi.getBindHistory(params),
    {
      manual: true
    }
  )
  
  return {
    bindingState,
    
    bindCodeData,
    bindCodeLoading,
    fetchBindCode,
    
    bindGameData,
    bindGameLoading,
    bindGame,
    
    bindHistoryData,
    bindHistoryLoading,
    fetchBindHistory
  }
}

/**
 * 示例：绑定页面中使用API服务
 */
export function useBindingPage() {
  const { bindingState, fetchBindCode, bindGame, bindCodeLoading, bindGameLoading } = useBinding()
  const gameId = ref(1)
  const uid = ref('')
  const isLoading = computed(() => bindCodeLoading || bindGameLoading)
  
  // 在页面加载时获取绑定码
  onMounted(() => {
    fetchBindCode({ gameId: gameId.value })
  })
  
  // 执行绑定操作
  const handleBind = async () => {
    if (!uid.value) {
      alert('请输入游戏ID')
      return
    }
    
    if (!bindingState.value.currentCode) {
      alert('绑定码未生成，请刷新页面')
      return
    }
    
    await bindGame({
      gameId: gameId.value,
      uid: uid.value,
      bindCode: bindingState.value.currentCode
    })
  }
  
  return {
    bindingState,
    isLoading,
    uid,
    handleBind
  }
} 