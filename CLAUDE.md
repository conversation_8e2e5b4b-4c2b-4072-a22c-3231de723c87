# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3 + TypeScript WeChat web application built with Vite, designed for game services including user authentication, game binding, recharge functionality, and rewards management. The application uses a modern, feature-based architecture with comprehensive tooling.

## Development Commands

### Core Development
```bash
pnpm dev                # Start development server (port 5174)
pnpm dev:host          # Start with host access
pnpm build             # Production build
pnpm preview           # Preview production build
```

### Code Quality & Testing
```bash
pnpm lint              # Run ESLint checks
pnpm lint:fix          # Fix ESLint issues automatically
pnpm typecheck         # Run TypeScript type checking
pnpm test              # Run Vitest unit tests
pnpm coverage          # Generate test coverage report
```

### Code Generation
```bash
pnpm auto:create       # Generate components/pages/stores using Plop templates
pnpm auto:remove       # Remove generated modules
pnpm release           # Automated release with changelog generation
```

## Project Architecture

### Technology Stack
- **Framework**: Vue 3 with Composition API and `<script setup>`
- **Build Tool**: Vite 6.3.5 with custom preset system (`/presets/`)
- **UI Library**: Varlet UI (@varlet/ui) with auto-import
- **CSS**: UnoCSS (atomic CSS) + custom design system
- **State Management**: Pinia with persistence (localStorage)
- **HTTP Client**: Custom axios wrapper in `/src/utils/http/`
- **Routing**: File-based routing with `unplugin-vue-router`
- **Package Manager**: PNPM (enforced via preinstall hook)

### Directory Structure & Patterns

#### Feature-Based Organization
Each major feature (auth, game, home, recharge, rewards, contact) follows this pattern:
- API module: `/src/api/[feature]/`
- Store: `/src/stores/[feature].ts`
- Pages: `/src/pages/[feature]/`
- Components: `/src/pages/[feature]/components/`
- Composables: `/src/pages/[feature]/composables/`

#### Key Directories
- `/src/api/` - Feature-based API modules with TypeScript interfaces
- `/src/stores/` - Pinia stores with persistence configuration
- `/src/composables/` - Reusable business logic (auto-imported)
- `/src/components/` - Global components (auto-imported)
- `/src/layouts/` - Layout system with default and custom layouts
- `/src/middleware/` - Route middleware (auth.ts, redirect.ts)
- `/src/plugins/` - Auto-loaded plugins (pinia, router, sentry, etc.)
- `/src/utils/` - Utility functions and HTTP client system

#### Auto-Import System
- **APIs**: Vue 3, VueUse, Pinia, Vue Router APIs are auto-imported
- **Components**: All components in `/src/components/` and Varlet UI components
- **Stores**: All stores from `/src/stores/` (e.g., `authStore()`, `userStore()`)
- **Composables**: All exports from `/src/composables/`
- **Custom APIs**: Project-specific API functions from `/src/api/`

### State Management Architecture

#### Store Structure
```typescript
// Example: /src/stores/auth.ts
export const useAuthStore = defineStore('auth', {
  state: () => ({
    loginStatus: LoginStatus.NOT_LOGGED,
    accessToken: '',
    refreshToken: '',
  }),
  persist: {
    enabled: true,
    storage: localStorage,
    key: '__giant__auth__info__',
  }
})
```

#### Key Stores
- `auth.ts` - Authentication state and token management
- `user.ts` - User profile and game information
- `game.ts` - Game-related state and bindings
- `app.ts` - Global application state and initialization
- `theme.ts` - Theme configuration

### API & HTTP Architecture

#### HTTP Client System (`/src/utils/http/`)
- Custom `HttpClient` class with interceptors
- Automatic token injection and refresh handling
- Business code handling (success/error codes)
- Event bus integration for error handling
- Public/private request separation
- Environment-based base URL configuration

#### API Structure
```typescript
// APIs are organized by feature and auto-imported
export const api = {
  user: userApi,
  game: gameApi,
  home: homeApi,
  recharge: rechargeApi,
  rewards: rewardsApi,
  contact: contactApi,
  binding: bindingApi
}
```

### Routing & Navigation

#### File-Based Routing Conventions
- `src/pages/index.vue` → `/`
- `src/pages/about.vue` → `/about`
- `src/pages/users/[id].vue` → `/users/:id`
- `src/pages/[...notFound].vue` → 404 catch-all

#### Route Protection
- **Authentication Middleware**: `/src/middleware/auth.ts`
- **Redirect Middleware**: `/src/middleware/redirect.ts`
- WeChat OAuth integration with token validation

#### Layout System
- Default layout: `/src/layouts/default.vue`
- Custom layouts via route meta: `{ meta: { layout: "custom" } }`

### Component Architecture

#### Component Patterns
- **Composition API**: All components use `<script setup lang="ts">`
- **Auto-Import**: Components in `/src/components/` are globally available
- **Co-location**: Page-specific components live in `/src/pages/[feature]/components/`
- **TypeScript**: Full type safety with proper props/emits typing

#### UI Framework Integration
- **Varlet UI**: Primary component library with auto-import
- **UnoCSS**: Atomic CSS classes and attribute mode
- **Design System**: Custom color palette and typography defined in `uno.config.ts`

### Environment Configuration

#### Environment Variables
```bash
# API Configuration
VITE_API_URL=https://dev-wx-service.ztgame.com/cgi-dev/
VITE_API_BASE_URL=/api

# WeChat Integration
VITE_APP_WECHAT_APP_ID=wx243c198990b665bd
VITE_APP_WECHAT_WEB_ID=wxd6766f2ba2a49dab

# Feature Flags
VITE_APP_MARKDOWN=true
VITE_APP_DEV_TOOLS=true
VITE_APP_API_AUTO_IMPORT=true
```

### Third-Party Integrations

#### Key Services
- **WeChat SDK**: OAuth authentication and JS-SDK utilities
- **Sentry**: Error monitoring and performance tracking
- **Microsoft Clarity**: User behavior analytics
- **Qiyu**: Customer service integration
- **PWA**: Progressive Web App capabilities

### Testing Strategy

#### Vitest Configuration
- Test files: `/src/test/`
- Unit testing with TypeScript support
- Snapshot testing capabilities
- Coverage reporting with c8

### Development Workflow

#### Code Generation
Use `pnpm auto:create` to generate standardized:
- Components with proper TypeScript typing
- Pages with routing configuration
- Stores with persistence setup
- API modules with type definitions

#### Code Quality
- **ESLint**: Configured with Vue 3 and TypeScript rules
- **Prettier**: Code formatting standards
- **Husky + lint-staged**: Pre-commit hooks for quality checks
- **TypeScript**: Full type safety across the application

### Deployment & Build

#### Build Configuration
- **Vite Preset System**: Custom configuration in `/presets/`
- **Legacy Support**: SWC for older browsers
- **PWA**: Service worker and manifest generation
- **Compression**: Gzip compression for production assets
- **Bundle Analysis**: Built-in analysis tools

## Important Notes

### WeChat Integration
- This app is designed specifically for WeChat environment
- Authentication flow depends on WeChat OAuth
- Different app IDs for development and production environments

### Package Management
- **PNPM Required**: Enforced via preinstall script
- Node.js version: >=20.12.2
- Package manager version: pnpm@8.15.8

### Development Patterns
- Always use Composition API with `<script setup>`
- Prefer composables over mixins for reusable logic
- Use auto-imports instead of manual imports where available
- Follow feature-based organization for new modules
- Use TypeScript strictly - no `any` types
- Implement proper error boundaries and loading states

## 你是一个经验丰富的vue3开发者，请根据以上内容进行代码的重构与编写，尽量优雅简洁符合最佳实践
  注意:
  - 代码的重构与编写尽量优雅简洁符合最佳实践
  - 文本答复以及代码的注释全部使用中文 
  - 代码的注释全部使用中文 
  - 创建页面与修改组件代码时，要时刻注意使用UnoCSS的属性模式来编写样式，并且注意移动端场景，要有响应式设计。
  - 要时刻节约上下文窗口，500行以下代码提供完整的代码文件，超过500行代码，提供部分代码文件，并给出完整的代码文件路径。
  - 一个对话中不要提供迭代代码，不利于代码合入与维护、一次性提供修改的代码再做解释
  - 简化提供的代码，专注于修复关键问题
  - 当你不确定或者不了解实时信息时，使用互联网搜索能力。
  - 每次修改完代码，需要你列出修改的文件或代码函数列表，标注已有或新增等。
  - 禁止提示语、日志、代码注释等使用IOS或Emoji表情。
  - 如果有部分不理解的地方，可以简单查询一下.cursor目录下的部分文档。
  - 如果修改的文件与代码有关联文档，需要修改完代码后，更新关联文档。