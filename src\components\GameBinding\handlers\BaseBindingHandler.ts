/**
 * 绑定处理器基类
 */

import { 
  BindingHandler, 
  BindingType, 
  BindingParams, 
  BindingResult, 
  GameBindingConfig,
  ValidationResult
} from '@/types/game-binding'
import { http } from '@/utils/http'

export abstract class BaseBindingHandler implements BindingHandler {
  abstract type: BindingType

  /**
   * 验证表单数据
   */
  async validateForm(formData: Record<string, any>, config: GameBindingConfig): Promise<boolean> {
    // 检查必填字段
    for (const field of config.formFields) {
      if (field.required && !formData[field.model]) {
        throw new Error(`请填写${field.label}`)
      }

      // 执行字段验证
      if (formData[field.model] && field.validation) {
        const value = formData[field.model]
        const validation = field.validation

        // 长度验证
        if (validation.minLength && value.length < validation.minLength) {
          throw new Error(`${field.label}长度不能少于${validation.minLength}位`)
        }
        if (validation.maxLength && value.length > validation.maxLength) {
          throw new Error(`${field.label}长度不能超过${validation.maxLength}位`)
        }

        // 正则验证
        if (validation.pattern && !new RegExp(validation.pattern).test(value)) {
          throw new Error(`${field.label}格式不正确`)
        }

        // 自定义验证
        if (validation.validator) {
          const result = validation.validator(value)
          if (result !== true) {
            throw new Error(typeof result === 'string' ? result : `${field.label}验证失败`)
          }
        }
      }
    }

    return true
  }

  /**
   * 执行绑定 - 抽象方法，由子类实现
   */
  abstract bind(params: BindingParams, config: GameBindingConfig): Promise<BindingResult>

  /**
   * 通用HTTP请求方法
   */
  protected async makeRequest<T = any>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    data?: any
  ): Promise<T> {
    try {
      let response
      switch (method) {
        case 'GET':
          response = await http.get<T>(url, { params: data })
          break
        case 'POST':
          response = await http.post<T>(url, data)
          break
        case 'PUT':
          response = await http.put<T>(url, data)
          break
        case 'DELETE':
          response = await http.delete<T>(url, { params: data })
          break
      }
      return response.data
    } catch (error: any) {
      console.error(`请求失败 [${method} ${url}]:`, error)
      throw new Error(error.message || '网络请求失败')
    }
  }

  /**
   * 验证账号信息（如果配置了验证端点）
   */
  protected async validateAccount(
    uid: string, 
    config: GameBindingConfig
  ): Promise<ValidationResult> {
    if (!config.apiConfig.validateEndpoint) {
      return { valid: true }
    }

    try {
      const result = await this.makeRequest<ValidationResult>(
        'GET',
        `${config.apiConfig.validateEndpoint}?uid=${uid}`
      )
      return result
    } catch (error: any) {
      return {
        valid: false,
        message: error.message || '账号验证失败'
      }
    }
  }

  /**
   * 格式化绑定结果
   */
  protected formatBindingResult(
    success: boolean,
    data?: any,
    message?: string
  ): BindingResult {
    return {
      success,
      message,
      data: success ? data : undefined
    }
  }
}
