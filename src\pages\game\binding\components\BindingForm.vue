<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import CustomSelect from '@/components/Select/index.vue'
import BindResult from '@/components/BindResult/index.vue'
import { useGameBinding } from '../composables/useGameBinding'
import { FieldType, BindingType } from '../types'

const router = useRouter()

// 使用游戏绑定组合式函数
const {
  loading,
  config,
  formData,
  bindingResult,
  verificationState,
  dynamicOptions,
  performBinding,
  sendVerificationCode,
  clearForm
} = useGameBinding()

// 计算属性
const showResult = computed(() => bindingResult.value !== null)

// 处理绑定按钮点击
const handleBindClick = async () => {
  await performBinding()
}

// 处理验证码按钮点击
const handleSendCodeClick = async () => {
  await sendVerificationCode()
}

// 处理结果确认
const handleResultConfirm = () => {
  if (bindingResult.value?.success) {
    // 绑定成功，可以跳转到其他页面
    router.push('/game/list')
  } else {
    // 绑定失败，清除结果继续尝试
    bindingResult.value = null
  }
}

// 处理结果重试
const handleResultRetry = () => {
  bindingResult.value = null
  clearForm()
}

// 获取字段的选项列表
const getFieldOptions = (fieldModel: string) => {
  const field = config.value?.formFields.find(f => f.model === fieldModel)
  if (field?.options) {
    return field.options
  }
  return dynamicOptions[fieldModel] || []
}
</script>

<template>
  <div class="binding-form-container h-full w-full relative">
    <!-- 绑定结果展示 -->
    <BindResult 
      v-if="showResult"
      :success="bindingResult!.success"
      :message="bindingResult!.success ? (bindingResult!.message || '绑定成功') : undefined"
      :error-message="bindingResult!.success ? undefined : bindingResult!.message"
      @confirm="handleResultConfirm"
      @retry="handleResultRetry"
    />

    <!-- 绑定表单 -->
    <div v-else-if="config" class="binding-form flex flex-col min-h-full w-full">
      <!-- 游戏Logo -->
      <div class="logo-container h-[180px] flex items-center justify-center">
        <var-image :src="config.logoUrl" width="120" />
      </div>

      <!-- 表单内容 -->
      <div class="form-content flex-grow px-8">
        <h2 class="text-xl font-medium text-center mb-6">{{ config.gameName }}账号绑定</h2>

        <!-- 表单字段 -->
        <div class="form-fields space-y-4">
          <div 
            v-for="field in config.formFields" 
            :key="field.model" 
            class="field-container"
          >
            <label class="field-label text-base font-normal text-[#1C1C1C] block mb-2">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500">*</span>
            </label>

            <!-- 选择器 -->
            <CustomSelect
              v-if="field.type === FieldType.SELECT"
              v-model="formData[field.model]"
              :options="getFieldOptions(field.model)"
              :placeholder="field.placeholder"
              class="w-full"
            />

            <!-- 普通输入框 -->
            <div v-else class="input-container relative">
              <input
                v-model="formData[field.model]"
                :placeholder="field.placeholder"
                :type="field.type === FieldType.PHONE ? 'tel' : 'text'"
                class="w-full py-3 px-4 text-sm bg-[#EDEDED] rounded-lg border-none outline-none placeholder:text-[#B8B8B8] focus:bg-white focus:ring-2 focus:ring-[#FFD722]"
              >

              <!-- 验证码按钮 -->
              <div 
                v-if="field.hasVerificationButton && field.type === FieldType.VERIFICATION_CODE"
                class="absolute right-0 top-0 h-full flex items-center"
              >
                <var-button
                  class="h-full px-4 border-none rounded-none rounded-r-lg whitespace-nowrap"
                  :class="verificationState.isCountingDown ? 
                    'bg-[#F5F5F5] text-[#B8B8B8] cursor-not-allowed' : 
                    'bg-[#FFD722] text-[#333333] font-medium'"
                  :disabled="verificationState.isCountingDown"
                  @click="handleSendCodeClick"
                  :elevation="false"
                  text
                >
                  {{ verificationState.isCountingDown ? 
                    `${verificationState.countdown}秒后重试` : 
                    '获取验证码' }}
                </var-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 绑定按钮 -->
        <var-button
          type="primary"
          class="w-full h-12 rounded-xl bg-[#FFD722] text-[#333] font-medium text-base border-none mt-8"
          :loading="loading"
          @click="handleBindClick"
          :elevation="false"
          text
        >
          {{ loading ? '绑定中...' : (config.buttonText || '绑定账号') }}
        </var-button>
      </div>

      <!-- 底部客服链接 -->
      <div class="footer-links flex items-center justify-center py-6">
        <div class="text-sm text-[#B8B8B8]">
          遇到问题？点此
          <span class="text-[#FFD722] cursor-pointer" @click="router.push('/contact')">联系客服</span>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="loading-container flex justify-center items-center h-full">
      <var-loading type="circle" color="#FFD722" size="large" />
    </div>
  </div>
</template>

<style scoped>
.binding-form-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.field-container {
  margin-bottom: 1rem;
}

.input-container input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 215, 34, 0.3);
}

/* 自定义按钮样式 */
:deep(.var-button--text) {
  border: none !important;
}

:deep(.var-button--loading) {
  opacity: 0.7;
}
</style>
