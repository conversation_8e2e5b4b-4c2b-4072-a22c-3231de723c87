/* 简化的HTTP封装 - 统一认证和公共API */
// 功能: 提供统一的HTTP客户端，支持auth参数控制认证

import { HttpClient } from './httpClient'
import type { HttpClientConfig } from './config'
import type { ApiResponse } from './types'

export * from './types'
export * from './eventBus'
export * from './config'

// 默认全局实例
export const http = new HttpClient()

// 创建自定义实例
export const createHttp = (config: Partial<HttpClientConfig> = {}) => new HttpClient(config)

// 统一请求方法 - 类似原生axios的使用方式
export const request = <R = any>(config: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: any
  params?: any
  headers?: any
  auth?: boolean  // 改为更直观的auth参数，默认true需要认证
  timeout?: number
  [key: string]: any
}): Promise<ApiResponse<R>> => {
  // 转换auth参数为内部的skipAuth
  const { auth = true, ...restConfig } = config
  return http.request<R>({
    ...restConfig,
    skipAuth: !auth  // auth=false时skipAuth=true
  })
}

// 便捷方法 - 需要认证的请求（默认）
export const get = <R = any>(url: string, config?: Omit<Parameters<typeof request>[0], 'url' | 'method'>) =>
  request<R>({ ...config, url, method: 'GET' })

export const post = <R = any>(url: string, data?: any, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'data'>) =>
  request<R>({ ...config, url, method: 'POST', data })

export const put = <R = any>(url: string, data?: any, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'data'>) =>
  request<R>({ ...config, url, method: 'PUT', data })

export const del = <R = any>(url: string, config?: Omit<Parameters<typeof request>[0], 'url' | 'method'>) =>
  request<R>({ ...config, url, method: 'DELETE' })

// 公共API方法 - 无需认证
export const publicGet = <R = any>(url: string, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'auth'>) =>
  request<R>({ ...config, url, method: 'GET', auth: false })

export const publicPost = <R = any>(url: string, data?: any, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'data' | 'auth'>) =>
  request<R>({ ...config, url, method: 'POST', data, auth: false })

export const publicPut = <R = any>(url: string, data?: any, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'data' | 'auth'>) =>
  request<R>({ ...config, url, method: 'PUT', data, auth: false })

export const publicDel = <R = any>(url: string, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'auth'>) =>
  request<R>({ ...config, url, method: 'DELETE', auth: false })

// FormData 便捷方法
export const postForm = <R = any>(url: string, formData: FormData, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'data'>) =>
  request<R>({ ...config, url, method: 'POST', data: formData })

export const putForm = <R = any>(url: string, formData: FormData, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'data'>) =>
  request<R>({ ...config, url, method: 'PUT', data: formData })

export const publicPostForm = <R = any>(url: string, formData: FormData, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'data' | 'auth'>) =>
  request<R>({ ...config, url, method: 'POST', data: formData, auth: false })

export const publicPutForm = <R = any>(url: string, formData: FormData, config?: Omit<Parameters<typeof request>[0], 'url' | 'method' | 'data' | 'auth'>) =>
  request<R>({ ...config, url, method: 'PUT', data: formData, auth: false })