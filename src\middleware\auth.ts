import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAuthStore, LoginStatus } from '@/stores/auth'
import { useGameStore } from '@/stores/game'
import { useSubscribeStore } from '@/stores/subscribe'
import WechatAuth from '~/utils/wechatAuth'
import { parse, stringify } from 'qs'

// 配置接口
export interface WechatAuthConfig {
  appId: string
  webId: string
}

// 创建认证实例
const createWechatAuth = (config: WechatAuthConfig) => 
  new WechatAuth({
    appid: config.appId,
    webid: config.webId,
  })

// URL 处理
const processUrl = () => {
  const url = window.location.href

  // 拆分出 hash（#xxx）
  const [urlWithoutHash, hashFragment = ''] = url.split('#')

  // 再拆 query
  const [baseUrl, queryString] = urlWithoutHash.split('?')
  const params = parse(queryString || '')

  if (params.code || params.state) {
    delete params.code
    delete params.state
    const query = stringify(params)
    return `${baseUrl}${query ? `?${query}` : ''}${hashFragment ? `#${hashFragment}` : ''}`
  }

  // 无需处理，原样返回
  return url
}

// 检查订阅状态
const checkSubscribeStatus = async (to: RouteLocationNormalized): Promise<boolean> => {
  const subscribeStore = useSubscribeStore()
  const SUBSCRIBE_CHECK_INTERVAL = 5 * 60 * 1000

  const lastCheckTime = subscribeStore.lastCheckTime
  const now = Date.now()

  if (lastCheckTime && (now - lastCheckTime < SUBSCRIBE_CHECK_INTERVAL)) {
    return subscribeStore.isSubscribed
  }

  try {
    const isSubscribed = await subscribeStore.checkSubscribeStatus()
    subscribeStore.setLastCheckTime(now)
    return isSubscribed
  }
  catch (error) {
    console.error('检查关注状态失败:', error)
    return false
  }
}

// 移除复杂的token检查函数，简化为使用store中的getter

// 重置认证状态
const resetAuthState = () => {
  const authStore = useAuthStore()
  const userStore = useUserStore()
  const gameStore = useGameStore()
  
  authStore.setLoginStatus(LoginStatus.NOT_LOGGED)
  authStore.clearTokens()
  userStore.clearUserInfo()
  gameStore.clearGameData()
  
  sessionStorage.removeItem('__giant__wx__authInProgress')
}

// 处理微信认证
const handleWechatAuth = (wechatAuth: WechatAuth) => async (to: RouteLocationNormalized) => {
  const authStore = useAuthStore()
  wechatAuth.redirectUri = processUrl()
  authStore.setLoginStatus(LoginStatus.AUTH_IN_PROGRESS)
  sessionStorage.setItem('__giant__wx__authInProgress', 'true')
  window.location.href = wechatAuth.authUrl
}

// 处理认证回调
const handleAuthCallback = (wechatAuth: WechatAuth) => 
  async (to: RouteLocationNormalized, next: NavigationGuardNext) => {
    const authStore = useAuthStore()
    const userStore = useUserStore()
    
    try {
      wechatAuth.returnFromWechat(to.fullPath)
      const loginResult = await authStore.handleLogin({ code: wechatAuth.code })
      
      // 更新用户信息
      if (loginResult.user) {
        userStore.setUserInfoFromAuth(loginResult.user)
      }
      
      authStore.setLoginStatus(LoginStatus.LOGGED_IN)

      if (!(to.meta.needSubscribe === false || to.meta.auth === false)) {
        const isSubscribed = await checkSubscribeStatus(to)
        if (!isSubscribed) {
          next('/qrcode/notSubscribe')
          return
        }
      }

      // 检查是否有保存的重定向路径
      const redirectPath = sessionStorage.getItem('__giant__redirect_after_auth__')
      if (redirectPath && redirectPath !== to.fullPath) {
        sessionStorage.removeItem('__giant__redirect_after_auth__')
        next(redirectPath)
        return
      }

      next()
    }
    catch (error) {
      console.error('微信认证回调处理失败:', error)
      resetAuthState()
      await handleWechatAuth(wechatAuth)(to)
    }
  }

// 简化的已登录状态处理
const handleLoggedInState = async (
  to: RouteLocationNormalized,
  next: NavigationGuardNext,
): Promise<void> => {
  const authStore = useAuthStore()
  authStore.setNeedsReauth(false)
  sessionStorage.removeItem('__giant__wx__authInProgress')

  // 简化：只检查基本的token有效性，具体的刷新由HTTP层处理
  if (!authStore.hasValidToken) {
    // 静默处理token无效情况，无需用户感知
    authStore.setNeedsReauth(true)
    authStore.setLoginStatus(LoginStatus.NOT_LOGGED)
    return next() // 触发路由守卫的重新执行
  }

  if (to.meta.auth !== false && to.meta.needSubscribe === true) {
    const isSubscribed = await checkSubscribeStatus(to)
    if (!isSubscribed) {
      next('/qrcode/notSubscribe')
      return
    }
  }
  next()
}

// 创建认证处理器
export const createAuthHandler = (config: WechatAuthConfig) => {
  const wechatAuth = createWechatAuth(config)
  
  return async (to: RouteLocationNormalized, next: NavigationGuardNext) => {
    const authStore = useAuthStore()
    const loginStatus = authStore.loginStatus

    try {
      switch (loginStatus) {
        case LoginStatus.NOT_LOGGED:
          await handleWechatAuth(wechatAuth)(to)
          break

        case LoginStatus.AUTH_IN_PROGRESS:
          await handleAuthCallback(wechatAuth)(to, next)
          break

        case LoginStatus.LOGGED_IN:
          await handleLoggedInState(to, next)
          break

        default:
          console.error('未知的登录状态:', loginStatus)
          resetAuthState()
          await handleWechatAuth(wechatAuth)(to)
      }
    }
    catch (error) {
      console.error('认证过程发生错误:', error)
      resetAuthState()
      next('/error')
    }
  }
}

// 认证中间件
export default (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
  // 如果不需要认证，直接通过
  if (to.meta.auth === false) {
    return next()
  }

  const authHandler = createAuthHandler({
    appId: import.meta.env.VITE_APP_WECHAT_APP_ID,
    webId: import.meta.env.VITE_APP_WECHAT_WEB_ID,
  })

  return authHandler(to, next)
} 