import { http, createApi } from '@/utils/http'
import { createMockApi } from '@/utils/mock'
import { BindKeyInfo } from '@/api/game/types'
import { generateMockBindKey } from '@/api/game/mock'

// 绑定游戏请求参数
export interface BindGameParams {
  uid: string
  bindKey: string
}

// 绑定游戏响应结果
export interface BindGameResult {
  success: boolean
  message?: string
  accountInfo?: {
    id: string
    name: string
  }
}

// 月圆之夜(5199)游戏绑定API
export const binding5199Api = createApi({
  // 获取绑定密钥
  getBindKey: () => http.get<BindKeyInfo>('/api/games/5199/bindKey'),
  
  // 校验账号
  validateAccount: (uid: string) => http.get<{ valid: boolean, message?: string }>(`/api/games/5199/validate?uid=${uid}`),
  
  // 提交绑定
  bindGame: (data: BindGameParams) => http.post<BindGameResult>('/api/games/5199/bind', data)
})

// 创建模拟API
export const mockBinding5199Api = createMockApi(binding5199Api)

// 模拟校验用户ID（简单规则：长度为8-12位且只包含数字和字母）
export function mockValidateAccount(uid: string): { valid: boolean, message?: string } {
  if (!uid) {
    return { valid: false, message: '账号ID不能为空' }
  }
  
  if (uid.length < 8 || uid.length > 12) {
    return { valid: false, message: '账号ID长度必须为8-12位' }
  }
  
  if (!/^[a-zA-Z0-9]+$/.test(uid)) {
    return { valid: false, message: '账号ID只能包含字母和数字' }
  }
  
  return { valid: true }
}

// 模拟绑定游戏
export function mockBindGame(params: BindGameParams): BindGameResult {
  // 校验账号格式
  const validateResult = mockValidateAccount(params.uid)
  if (!validateResult.valid) {
    return {
      success: false,
      message: validateResult.message
    }
  }
  
  // 简单的密钥校验：必须以game-5199-开头
  if (!params.bindKey || !params.bindKey.startsWith('game-5199-')) {
    return {
      success: false,
      message: '绑定密钥无效'
    }
  }
  
  // 模拟成功结果
  return {
    success: true,
    accountInfo: {
      id: params.uid,
      name: `玩家${params.uid.substring(0, 4)}**`
    }
  }
} 