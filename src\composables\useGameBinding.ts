/**
 * 游戏绑定组合式函数
 */

import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToast } from '@/composables/useToast'
import {
  GameBindingConfig,
  BindingResult,
  BindingParams,
  FieldType,
  BindingType
} from '@/types/game-binding'
import { getGameBindingConfig } from '@/config/game-binding'
import { getBindingHandler } from '@/components/GameBinding/handlers'

export function useGameBinding() {
  const route = useRoute()
  const router = useRouter()
  const toast = useToast()

  // 状态管理
  const loading = ref(false)
  const config = ref<GameBindingConfig | null>(null)
  const formData = reactive<Record<string, any>>({})
  const currentTab = ref(0)
  const bindingResult = ref<BindingResult | null>(null)

  // 获取游戏ID
  const gameId = computed(() => {
    return route.params.gameId as string || route.query.gameId as string
  })

  // 获取游戏配置
  const loadGameConfig = async () => {
    if (!gameId.value) {
      toast.error('游戏ID不能为空')
      return false
    }

    const gameConfig = getGameBindingConfig(gameId.value)
    if (!gameConfig) {
      toast.error(`不支持的游戏: ${gameId.value}`)
      return false
    }

    config.value = gameConfig
    
    // 初始化表单数据
    initFormData()
    
    return true
  }

  // 初始化表单数据
  const initFormData = () => {
    if (!config.value) return

    const fields = getCurrentFields()
    fields.forEach(field => {
      formData[field.model] = ''
    })
  }

  // 获取当前标签页的字段
  const getCurrentFields = () => {
    if (!config.value) return []

    // 如果支持标签页且当前标签页有自定义字段
    if (config.value.customConfig?.supportTabs && config.value.customConfig.tabs) {
      const currentTabConfig = config.value.customConfig.tabs[currentTab.value]
      if (currentTabConfig && currentTabConfig.fields.length > 0) {
        return currentTabConfig.fields
      }
    }

    return config.value.formFields
  }

  // 获取当前绑定类型
  const getCurrentBindingType = () => {
    if (!config.value) return config.value?.bindingType

    // 如果支持标签页
    if (config.value.customConfig?.supportTabs && config.value.customConfig.tabs) {
      const currentTabConfig = config.value.customConfig.tabs[currentTab.value]
      return currentTabConfig?.type || config.value.bindingType
    }

    return config.value.bindingType
  }

  // 执行绑定
  const performBinding = async () => {
    if (!config.value || loading.value) return

    loading.value = true
    bindingResult.value = null

    try {
      const bindingType = getCurrentBindingType()
      const handler = getBindingHandler(bindingType)

      const params: BindingParams = {
        gameId: gameId.value,
        formData: { ...formData },
        additionalData: {
          tabIndex: currentTab.value,
          ...route.query
        }
      }

      const result = await handler.bind(params, config.value)
      bindingResult.value = result

      if (result.success) {
        toast.success(result.message || '绑定成功')
        // 可以在这里添加成功后的逻辑，如跳转页面
      } else {
        toast.error(result.message || '绑定失败')
      }

      return result

    } catch (error: any) {
      console.error('绑定失败:', error)
      toast.error(error.message || '绑定失败，请稍后重试')
      return {
        success: false,
        message: error.message || '绑定失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 处理自定义按钮点击
  const handleCustomButtonClick = async (action: string, fieldModel?: string) => {
    if (!config.value) return

    const bindingType = getCurrentBindingType()
    const handler = getBindingHandler(bindingType)
    const customComponents = handler.getCustomComponents?.()

    if (!customComponents || !customComponents[action]) {
      console.warn(`未找到自定义操作: ${action}`)
      return
    }

    try {
      switch (action) {
        case 'sendVerificationCode':
          if (fieldModel && formData.phoneNumber) {
            const smsHandler = handler as any
            const result = await smsHandler.sendVerificationCode(formData.phoneNumber, config.value)
            if (result.success) {
              toast.success(result.message || '验证码已发送')
            } else {
              toast.error(result.message || '发送验证码失败')
            }
          }
          break

        case 'splitInputContent':
          const apiHandler = handler as any
          const splitResult = customComponents[action](formData)
          Object.assign(formData, splitResult)
          toast.success('数据已自动填入')
          break

        default:
          console.warn(`未处理的自定义操作: ${action}`)
      }
    } catch (error: any) {
      toast.error(error.message || '操作失败')
    }
  }

  // 切换标签页
  const switchTab = (index: number) => {
    currentTab.value = index
    initFormData() // 重新初始化表单数据
  }

  // 清空表单
  const clearForm = () => {
    Object.keys(formData).forEach(key => {
      formData[key] = ''
    })
  }

  // 验证表单
  const validateForm = async () => {
    if (!config.value) return false

    const bindingType = getCurrentBindingType()
    const handler = getBindingHandler(bindingType)

    try {
      await handler.validateForm(formData, config.value)
      return true
    } catch (error: any) {
      toast.error(error.message)
      return false
    }
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    // 清理定时器等资源
    try {
      const smsHandler = getBindingHandler(BindingType.SMS) as any
      if (smsHandler && typeof smsHandler.destroy === 'function') {
        smsHandler.destroy()
      }
    } catch (error) {
      // 忽略清理时的错误
    }
  })

  return {
    // 状态
    loading,
    config,
    formData,
    currentTab,
    bindingResult,
    gameId,

    // 计算属性
    getCurrentFields,
    getCurrentBindingType,

    // 方法
    loadGameConfig,
    performBinding,
    handleCustomButtonClick,
    switchTab,
    clearForm,
    validateForm
  }
}
