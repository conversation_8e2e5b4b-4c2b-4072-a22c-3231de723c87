/**
 * API绑定处理器
 * 处理通过UID和昵称进行的游戏账号绑定
 */

import { 
  BindingType, 
  BindingParams, 
  BindingResult, 
  GameBindingConfig 
} from '@/types/game-binding'
import { BaseBindingHandler } from './BaseBindingHandler'

export class ApiBindingHandler extends BaseBindingHandler {
  type = BindingType.API

  /**
   * 执行API绑定
   */
  async bind(params: BindingParams, config: GameBindingConfig): Promise<BindingResult> {
    const { gameId, formData } = params

    try {
      // 验证表单数据
      await this.validateForm(formData, config)

      // 特殊处理：5199游戏的官服绑定（使用密钥）
      if (gameId === '5199' && !formData.gameUid) {
        return await this.handleBindKeyBinding(params, config)
      }

      // 如果配置了验证端点，先验证账号
      if (config.apiConfig.validateEndpoint && formData.gameUid) {
        const validateResult = await this.validateAccount(formData.gameUid, config)
        if (!validateResult.valid) {
          return this.formatBindingResult(false, null, validateResult.message)
        }
      }

      // 准备绑定数据
      const bindData = this.prepareBindData(formData, config)

      // 调用绑定API
      const response = await this.makeRequest<any>(
        'POST',
        config.apiConfig.bindEndpoint,
        bindData
      )

      // 处理响应
      if (response.success !== false) {
        return this.formatBindingResult(true, {
          gameId,
          characterName: formData.roleName || formData.nickname,
          accountUid: formData.gameUid || formData.gameId,
          bindingId: response.bindingId
        }, '绑定成功')
      } else {
        return this.formatBindingResult(false, null, response.message || '绑定失败')
      }

    } catch (error: any) {
      console.error('API绑定失败:', error)
      return this.formatBindingResult(false, null, error.message || '绑定失败，请稍后重试')
    }
  }

  /**
   * 处理密钥绑定（5199游戏官服）
   */
  private async handleBindKeyBinding(
    params: BindingParams, 
    config: GameBindingConfig
  ): Promise<BindingResult> {
    try {
      // 获取绑定密钥
      const bindKeyResponse = await this.makeRequest<{ bindCode: string }>(
        'GET',
        '/api/games/5199/bindKey'
      )

      if (!bindKeyResponse.bindCode) {
        return this.formatBindingResult(false, null, '获取绑定密钥失败')
      }

      // 使用密钥进行绑定
      const bindData = {
        bindKey: bindKeyResponse.bindCode,
        channel: 'official'
      }

      const response = await this.makeRequest<any>(
        'POST',
        config.apiConfig.bindEndpoint,
        bindData
      )

      if (response.success !== false) {
        return this.formatBindingResult(true, {
          gameId: params.gameId,
          bindKey: bindKeyResponse.bindCode,
          bindingId: response.bindingId
        }, '密钥绑定成功')
      } else {
        return this.formatBindingResult(false, null, response.message || '密钥绑定失败')
      }

    } catch (error: any) {
      console.error('密钥绑定失败:', error)
      return this.formatBindingResult(false, null, error.message || '密钥绑定失败')
    }
  }

  /**
   * 准备绑定数据
   */
  private prepareBindData(formData: Record<string, any>, config: GameBindingConfig): any {
    const bindData: any = {
      gameId: config.gameId
    }

    // 根据不同游戏的字段映射
    switch (config.gameId) {
      case '5199':
        bindData.uid = formData.gameUid
        bindData.roleName = formData.roleName
        bindData.channel = 'other'
        break
      
      case '5218':
        bindData.roleName = formData.roleName
        bindData.gameId = formData.gameId
        break
      
      case '5286':
        bindData.nickname = formData.nickname
        bindData.gameId = formData.gameId
        bindData.server = formData.server
        break
      
      default:
        // 通用映射
        Object.keys(formData).forEach(key => {
          bindData[key] = formData[key]
        })
    }

    return bindData
  }

  /**
   * 获取自定义组件（用于特殊功能按钮）
   */
  getCustomComponents() {
    return {
      // 5286游戏的快速填入功能
      splitInputContent: (formData: Record<string, any>) => {
        const customInput = formData.customInput
        if (!customInput) {
          throw new Error('请先输入需要分割的数据')
        }

        const parts = customInput.split('|')
        if (parts.length >= 2) {
          return {
            nickname: parts[0].trim(),
            gameId: parts[1].trim()
          }
        } else {
          throw new Error('输入格式错误，请使用"昵称|ID"格式')
        }
      }
    }
  }
}
