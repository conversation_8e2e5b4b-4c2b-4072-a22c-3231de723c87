<script setup lang="ts">
import { ref, reactive, onUnmounted } from 'vue'
import BindForm from '@/components/BindForm/index.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const bindFormRef = ref()

// 游戏信息
const gameInfo = reactive({
	id: route.query.gameId as string || '',
	name: route.query.gameName as string || '游戏名称',
	logo: route.query.logo as string || '/images/default-game-logo.png'
})

// 验证码状态
const countdownActive = ref(false)
const countdown = ref(60)
const countdownTimer = ref<NodeJS.Timeout | null>(null)

// 表单字段定义
const formFields = [
	{
		model: 'phoneNumber',
		label: '手机号',
		placeholder: '请输入手机号',
		required: true
	},
	{
		model: 'verificationCode',
		label: '验证码',
		placeholder: '请输入短信验证码',
		required: true
	}
]

// 额外数据
const additionalData = {
	gameId: gameInfo.id
}

// 请求验证码函数
async function requestVerificationCode() {
	if (countdownActive.value) return
	
	const phoneNumber = bindFormRef.value?.data.FormData.phoneNumber
	
	if (!phoneNumber) {
		console.error('请先输入手机号')
		return
	}
	
	try {
		// 这里调用实际的验证码API
		// const response = await api.sendVerificationCode(phoneNumber)
		console.log('发送验证码到手机号:', phoneNumber)
		
		// 启动倒计时
		countdownActive.value = true
		countdown.value = 60
		
		countdownTimer.value = setInterval(() => {
			countdown.value--
			if (countdown.value <= 0) {
				clearInterval(countdownTimer.value as NodeJS.Timeout)
				countdownActive.value = false
			}
		}, 1000)
	}
	catch (error) {
		console.error('获取验证码失败:', error)
		countdownActive.value = false
	}
}

// 绑定游戏账号
async function bindGameAccount(formData: Record<string, any>) {
	try {
		// 这里调用实际的绑定API
		// const response = await api.bindGameAccount({
		//   gameId: gameInfo.id,
		//   phoneNumber: formData.phoneNumber,
		//   code: formData.verificationCode,
		//   accountId: formData.accountId
		// })
		
		console.log('绑定游戏账号:', formData)
		
		// 模拟返回数据
		return {
			game_id: gameInfo.id,
			account_uid: formData.accountId
		}
	}
	catch (error) {
		console.error('绑定失败:', error)
		return null
	}
}

// 组件销毁时清除计时器
onUnmounted(() => {
	if (countdownTimer.value) {
		clearInterval(countdownTimer.value)
		countdownTimer.value = null
	}
})
</script>

<template>
	<div class="h-full w-full bg-gray-50">
		<BindForm
			:logo-src="gameInfo.logo"
			:input-fields="formFields"
			:bind-function="bindGameAccount"
			:additional-data="additionalData"
			:custom-button-string="'绑定'"
			ref="bindFormRef"
		>
			<!-- 使用命名插槽为验证码字段添加自定义按钮 -->
			<template #custom-button-verificationCode>
				<div class="absolute right-0 top-0 h-full flex items-center">
					<var-button
						class="w-24 h-[40px] border-none rounded-none rounded-r-4 whitespace-nowrap"
						:class="countdownActive ? 'bg-[#F5F5F5] text-[#B8B8B8] cursor-not-allowed' : 'bg-[#FFD722] text-[#333333] font-medium'"
						:disabled="countdownActive"
						@click.prevent="requestVerificationCode"
						:elevation="false"
						text
					>
						{{ countdownActive ? `${countdown}秒后重试` : '获取验证码' }}
					</var-button>
				</div>
			</template>
		</BindForm>
	</div>
</template>

<style scoped>
/* 所有样式已使用UnoCSS原子类替代 */
</style>

<route lang="json">
{
	"meta": {
		"title": "账号绑定",
		"layout": "backHeader"
	}
}
</route>


