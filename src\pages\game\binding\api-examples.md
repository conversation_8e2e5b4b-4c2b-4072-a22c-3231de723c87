# 游戏绑定API示例

## 动态服务器选择API

### 请求
```
GET /api/games/5286/servers
```

### 响应
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "value": "server1",
      "label": "华东一区"
    },
    {
      "value": "server2", 
      "label": "华南一区"
    },
    {
      "value": "server3",
      "label": "华北一区"
    },
    {
      "value": "server4",
      "label": "西南一区"
    }
  ]
}
```

## 绑定API

### API绑定请求
```
POST /api/games/5218/bind
Content-Type: application/json

{
  "gameId": "5218",
  "roleName": "测试角色",
  "gameId": "*********"
}
```

### API绑定响应
```json
{
  "code": 200,
  "message": "绑定成功",
  "data": {
    "success": true,
    "message": "绑定成功",
    "data": {
      "gameId": "5218",
      "characterName": "测试角色",
      "accountUid": "*********",
      "bindingId": "bind_123456"
    }
  }
}
```

## 短信验证绑定

### 发送验证码
```
POST /api/games/5078/sendCode
Content-Type: application/json

{
  "phoneNumber": "***********",
  "gameId": "5078"
}
```

### 发送验证码响应
```json
{
  "code": 200,
  "message": "验证码已发送",
  "data": {
    "success": true,
    "message": "验证码已发送"
  }
}
```

### 短信绑定请求
```
POST /api/games/5078/bind
Content-Type: application/json

{
  "gameId": "5078",
  "phoneNumber": "***********",
  "verificationCode": "123456"
}
```

### 短信绑定响应
```json
{
  "code": 200,
  "message": "绑定成功",
  "data": {
    "success": true,
    "message": "绑定成功",
    "data": {
      "gameId": "5078",
      "accountUid": "***********",
      "bindingId": "bind_789012"
    }
  }
}
```

## 账号验证API

### 验证请求
```
GET /api/games/5199/validate?uid=abc123def456
```

### 验证响应
```json
{
  "code": 200,
  "message": "验证成功",
  "data": {
    "valid": true,
    "message": "账号验证成功"
  }
}
```

### 验证失败响应
```json
{
  "code": 200,
  "message": "验证失败",
  "data": {
    "valid": false,
    "message": "账号不存在或格式错误"
  }
}
```
