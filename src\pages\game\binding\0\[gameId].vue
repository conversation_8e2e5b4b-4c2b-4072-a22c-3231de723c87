<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BindForm from '@/components/BindForm/index.vue'
import BindResult from '@/components/BindResult/index.vue'
import { useGameAuth } from './composables/useGameAuth'
// 导入API服务
import { useBindingService } from '@/api/game'

// 获取路由参数
const route = useRoute()
const router = useRouter()
const gameId = route.params.gameId
const bindFormRef = ref()

// 获取绑定服务
const { bindingState } = useBindingService()

// 获取游戏验证和绑定功能
const { 
  redirectToGiantAuth, 
  bindWithOtherChannel,
  fetchGameServers,
  checkAndHandleAuth,
  bindingSuccess,
  bindingError,
  isBinding,
  token,
  user
} = useGameAuth()

// 显示状态管理
const showBindForm = ref(false)
const showBindResult = ref(false)
const showAuthButtons = computed(() => !showBindForm.value && !showBindResult.value && !bindingSuccess.value)

// 游戏信息
const gameInfo = reactive({
  id: gameId,
  name: '游戏名称', // 实际应用中应从API获取
  logo: '/images/default-game-logo.png' // 实际应用中应从API获取
})

// 表单字段定义
const formFields = ref([
  {
    model: 'server',
    label: '选择服务器',
    placeholder: '请选择服务器',
    type: 'select',
    options: [],
    required: true
  },
  {
    model: 'roleId',
    label: '角色ID',
    placeholder: '请输入角色ID',
    required: true
  },
  {
    model: 'roleName',
    label: '角色名称',
    placeholder: '请输入角色名称',
    required: true
  }
])

// 额外数据
const additionalData = reactive({
  gameId: gameInfo.id
})

// 获取服务器列表
async function loadServers() {
  const servers = await fetchGameServers(gameId)
  formFields.value[0].options = servers
}

// 切换到其他渠道验证
function showOtherChannelAuth() {
  showBindForm.value = true
}

// 处理绑定结果
function handleBindResult(success: boolean) {
  showBindResult.value = true
  showBindForm.value = false
}

// 处理表单绑定成功
function handleBindFormSuccess(userData) {
  console.log('表单绑定成功:', userData)
  showBindResult.value = true
  showBindForm.value = false
}

// 处理表单绑定失败
function handleBindFormError(error) {
  console.error('表单绑定失败:', error)
}

// 处理结果确认
function handleResultConfirm() {
  // 跳转到首页或其他页面
  router.push('/')
}

// 初始化
onMounted(async () => {
  // 加载服务器列表
  await loadServers()
  
  // 检查 URL 参数，处理自动验证或回调
  checkAndHandleAuth(gameId)
  
  // 如果 URL 中有 token 和 user 参数（验证回调）
  if (token.value && user.value) {
    showBindResult.value = true
  }
})
</script>

<template>
  <div class="h-full w-full bg-gray-50">
    <!-- 验证选择按钮界面 -->
    <div v-if="showAuthButtons" class="flex flex-col items-center justify-center p-8 h-full">
      <var-image :src="gameInfo.logo" width="186" class="mb-8" />
      
      <div class="text-xl font-medium mb-8 text-center">
        选择验证方式进行绑定
      </div>
      
      <!-- 巨人通行证验证按钮 -->
      <var-button
        class="h-[48px] w-full rounded-2xl bg-[#FFD722] text-[#333] font-medium text-base border-none"
        :loading="isBinding"
        :elevation="false"
        text
        @click="redirectToGiantAuth"
      >
        <var-icon name="shield-account" />
        巨人通行证验证
      </var-button>
      
      <!-- 其他渠道验证按钮 -->
      <var-button
        class="mt-4 h-[48px] w-full rounded-2xl bg-[#F5F5F5] text-[#666] font-medium text-base border-none"
        :elevation="false"
        text
        @click="showOtherChannelAuth"
      >
        <var-icon name="account-details" />
        其他渠道验证
      </var-button>
    </div>
    
    <!-- 其他渠道验证表单 -->
    <!-- 返回验证方式选择按钮 -->
    <div v-if="showBindForm" class="mb-10 px-4">
      <var-button type="warning"  @click="showBindForm=!showBindForm">
        <var-icon name="chevron-left" />
        返回验证方式选择
      </var-button>
    </div>

    <BindForm
      v-if="showBindForm"
      :input-fields="formFields"
      :bind-function="bindWithOtherChannel"
      :additional-data="additionalData"
      :custom-button-string="'绑定账号'"
      ref="bindFormRef"
      @bind-success="handleBindFormSuccess"
      @bind-error="handleBindFormError"
    />
    
    <!-- 绑定结果展示 -->
    <BindResult 
      v-if="showBindResult || bindingSuccess"
      :success="bindingSuccess"
      :error-message="bindingError"
      button-text="返回首页"
      @confirm="handleResultConfirm"
      @retry="redirectToGiantAuth"
    />
  </div>
</template>

<style scoped>
/* 使用UnoCSS原子类 */
</style>

<route lang="json">
{
    "meta": {
        "title": "游戏账号绑定",
        "layout": "backHeader"
    }
}
</route>


