<script setup lang="ts">
import { ref, reactive } from 'vue'
import BindForm from '@/components/BindForm/index.vue'
import { useRoute } from 'vue-router'
import { binding5199Api } from './api'
import { useApiRequest } from '@/composables/useApiRequest'
import { mockBindGame, mockValidateAccount } from './api'

const route = useRoute()
const bindFormRef = ref()

// 游戏信息
const gameInfo = reactive({
  id: '5199',  // 固定为5199游戏ID
  name: route.query.gameName as string || '月圆之夜',
  logo: route.query.logo as string || '/images/default-game-logo.png'
})

// 表单字段定义 - 角色昵称和游戏ID
const formFields = [
  {
    model: 'roleName',
    label: '角色昵称',
    placeholder: '请输入游戏中的角色昵称',
    required: true
  },
  {
    model: 'gameUid',
    label: '游戏UID',
    placeholder: '请输入游戏中的UID',
    required: true
  }
]

// 额外数据
const additionalData = {
  gameId: gameInfo.id,
  channel: 'other'  // 标记为其他渠道
}

// 创建绑定请求
const { loading: bindLoading, run: runBindGame } = useApiRequest(
  (formData: { roleName: string, gameUid: string }) => 
    binding5199Api.bindGame({
      uid: formData.gameUid,
      bindKey: additionalData.bindKey || ''
    }),
  {
    manual: true,
    onError: (error) => {
      console.error('绑定游戏账号失败:', error)
    }
  }
)

// 创建验证请求
const { loading: validating, run: validateUid } = useApiRequest(
  (uid: string) => binding5199Api.validateAccount(uid),
  {
    manual: true,
    onError: (error) => {
      console.error('验证账号失败:', error)
    }
  }
)

// 绑定游戏账号
async function bindGameAccount(formData: Record<string, any>) {
  try {
    // 先验证UID
    const validateResult = await validateUid(formData.gameUid)
    
    if (!validateResult.valid) {
      throw new Error(validateResult.message || '游戏UID验证失败')
    }
    
    // 调用绑定API
    const result = await runBindGame(formData)
    
    if (!result.success) {
      throw new Error(result.message || '绑定失败')
    }
    
    // 返回绑定结果
    return {
      game_id: gameInfo.id,
      character_name: formData.roleName,
      account_uid: formData.gameUid,
      channel: 'other'
    }
  }
  catch (error) {
    console.error('绑定失败:', error)
    return null
  }
}
</script>

<template>
  <div class="h-full w-full bg-gray-50">
    <BindForm
      :logo-src="gameInfo.logo"
      :input-fields="formFields"
      :bind-function="bindGameAccount"
      :additional-data="additionalData"
      :custom-button-string="'绑定账号'"
      :loading="bindLoading || validating"
      ref="bindFormRef"
    />
  </div>
</template>

<style scoped>
/* 所有样式已使用UnoCSS原子类替代 */
</style>

<route lang="json">
{
  "meta": {
    "title": "5199角色绑定",
    "layout": "backHeader"
  }
}
</route> 