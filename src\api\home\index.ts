import { request } from '@/utils/http'
import * as types from './types'

/**
 * 首页模块API定义
 */
export const homeApi = {
  // 获取当前用户的绑定信息 - 真实API
  getUserBindings: () =>
    request<UserBindingInfo[]>({
      url: '/operation-wechat-service/u1/v1/home/<USER>',
      method: 'GET',
    }),

  // 游戏解绑 - 真实API
  unbindGame: (params: UnbindGameParams) =>
    request<string>({
      url: '/operation-wechat-service/u1/v1/home/<USER>',
      method: 'POST',
      data: params,
    }),

  // 切换激活游戏 - 真实API
  switchGame: (params: SwitchGameParams) =>
    request<null>({
      url: '/operation-wechat-service/u1/v1/home/<USER>',
      method: 'POST',
      data: params,
    }),

  // 以下为兼容性保留的API，实际项目中应该逐步迁移到真实API
  // 获取首页配置
  getHomeConfig: (params?: HomeConfigParams) =>
    request<HomeConfigResponse>({
      url: '/api/home/<USER>',
      method: 'GET',
      params,
    }),

  // 获取用户游戏和账户数据
  getGameData: (params?: GameDataParams) =>
    request<GameDataResponse>({
      url: '/api/user/games',
      method: 'GET',
      params,
    }),

  // 设置账户为激活状态
  setActiveAccount: (params: SetActiveAccountParams) =>
    request<BaseResponse<boolean>>({
      url: '/api/user/account/active',
      method: 'POST',
      data: params,
    }),

  // 绑定新账户
  bindAccount: (params: BindAccountParams) =>
    request<BaseResponse<boolean>>({
      url: '/api/user/account/bind',
      method: 'POST',
      data: params,
    }),

  // 解绑账户
  unbindAccount: (params: UnbindAccountParams) =>
    request<BaseResponse<boolean>>({
      url: '/api/user/account/unbind',
      method: 'POST',
      data: params,
    }),
}